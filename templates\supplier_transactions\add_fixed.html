{% extends "base.html" %}

{% block title %}{{ '�༭' if edit_mode else '����' }}��Ӧ������ - {{ app_name }}{% endblock %}

{% block page_title %}{% if edit_mode %}�༭��Ӧ������{% else %}��Ӧ������¼��{% endif %}{% endblock %}

{% block extra_css %}
<style>

/* ɾ�����������ʽ */

/* ɾ����ͻ�ı�ͷ��ʽ */

/* ��ͷ��ɫ - ʹ�ø�ǿ��ѡ���� */
#transactions-container .table thead th.header-return-material {
    background-color: #c2dbff !important; /* ��������ǳ��ɫ */
}
#transactions-container .table thead th.header-store-material {
    background-color: #bce4d0 !important; /* ��������ǳ��ɫ */
}
#transactions-container .table thead th.header-return-payment {
    background-color: #ffecb3 !important; /* ��������ǳ��ɫ */
}
#transactions-container .table thead th.header-store-payment {
    background-color: #f8c4c8 !important; /* ��������ǳ��/��ɫ */
}
#transactions-container .table thead th.header-action {
    background-color: #e9ecef !important; /* Ĭ�ϱ�ͷ��ɫ */
}

/* ���ɱ༭�ֶα���ɫ */
.readonly-field {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

/* �Ƴ��������������¼�ͷ */
/* Chrome, Safari, Edge, Opera */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
/* Firefox */
input[type="number"] {
    -moz-appearance: textfield;
}

/* ɾ��Ӱ������������ʽ */

/* ��Ӧ������¼������ʽϵͳ - ���Ƴ�������table��ʽ */



/* ��������ϸ�����̶��ߴ� */
#supplier-owed-materials {
    height: 100% !important;
    max-height: 100% !important;
    overflow-y: auto !important;
    border: none;
    border-radius: 0;
    background: white !important;
    position: relative;
    flex-shrink: 0;
}

/* ������ϸ�����ʽ */
#supplier-owed-materials .supplier-transaction-table {
    border-radius: 0 !important;
    box-shadow: none !important;
    border: none !important;
    background: white !important;
    background-color: white !important;
    background-image: none !important;
}

#supplier-owed-materials .supplier-transaction-table thead th {
    background-color: #6c757d !important;
    background: #6c757d !important;
    background-image: none !important;
    color: white !important;
    font-weight: 700 !important;
    font-size: 14px !important;
    padding: 8px 4px !important;
    height: auto !important;
    line-height: 1.2 !important;
    vertical-align: middle !important;
    text-align: center !important;
}

#supplier-owed-materials .supplier-transaction-table tbody td {
    padding: 8px 4px !important;
    font-size: 14px !important;
    font-weight: 700 !important;
    border-bottom: none !important;
    text-align: center !important;
    background: white !important;
    background-color: white !important;
    background-image: none !important;
    line-height: 1.2 !important;
    margin: 0 !important;
    border-spacing: 0 !important;
    vertical-align: middle !important;
    height: auto !important;
}

/* ������ϸ���������ʽ */
#supplier-owed-materials .supplier-transaction-table input,
#supplier-owed-materials .supplier-transaction-table select {
    font-weight: 700 !important;
    font-size: 14px !important;
    text-align: center !important;
    border: none !important;
    background: transparent !important;
    width: 100% !important;
}

/* ������ϸ��Ƭ������ʽ */
.col-md-3 .card {
    background: white !important;
    border: none !important;
}

.col-md-3 .card .card-header {
    background-color: #000000 !important;
    color: white !important;
    border-bottom: none !important;
}

.col-md-3 .card .card-body {
    background-color: white !important;
}

/* �򻯵ı����ʽ - ֻ�����Ҫ����ʽ */
.transaction-row {
    margin-bottom: 0 !important;
}

/* ɾ��Ӱ�����б�����ʽ */

/* �����ⴹֱ���� */
.transaction-row > div:first-child {
    width: 60px !important;
    text-align: left !important;
    padding-right: 10px !important;
    display: flex !important;
    align-items: center !important;
}

/* ɾ���ظ�����ʽ���� */

/* ɾ��Ӱ��ҳ�沼�ֵ���ʽ */

main {
    overflow-y: auto !important;
    max-height: 100vh !important;
}

.row {
    align-items: flex-start !important;
}

/* ========== ��Ӧ������¼��ҳ������Ӵ�Ӵ���ʽ ========== */
/* �����ʽͳһ */
.form-label {
    font-size: 14px !important;
    font-weight: 700 !important;
}

/* ɾ��Ӱ������������ʽ */

/* �Ƴ��������������¼�ͷ */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
input[type="number"] {
    -moz-appearance: textfield;
}

/* ɾ��select��ʽ */

/* ��ť��ʽ */
.btn-sm {
    font-size: 14px !important;
    font-weight: 700 !important;
    padding: 6px 12px !important;
}

/* ��Ƭ���ı���ʽ */
.card-header h6.fw-bold,
.card-body h6.fw-bold {
    font-size: 14px !important;
    font-weight: 700 !important;
}

.card-body .small,
.card-body span:not(.small) {
    font-size: 14px !important;
    font-weight: 600 !important;
}

/* ��Ӧ����Ϣ������ֵ��ʾ */
#previous-owed-gold, #current-owed-gold, #return-owed-gold, #total-owed-gold,
#previous-deposit-gold, #current-deposit-gold, #withdraw-deposit-gold, #total-deposit-gold,
#previous-owed-amount, #current-owed-amount, #return-owed-amount, #total-owed-amount,
#previous-deposit-amount, #current-deposit-amount, #withdraw-deposit-amount, #total-deposit-amount {
    font-size: 14px !important;
    font-weight: 700 !important;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- ��� CSRF token -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <!-- ������Ϣ���� - ������� -->
    <div class="row mb-3" style="border-bottom: 2px solid #ffc107; padding-bottom: 10px;">
        <div class="col">
            <div class="d-flex gap-3">
                <div class="flex-grow-1" style="width: 20%;">
                    <label for="business-date" class="form-label mb-1">¼��ʱ��:</label>
                    <input type="datetime-local" id="business-date" name="business_date" class="form-control form-control-sm"
                           value="{% if edit_mode %}{{ transaction.created_at.strftime('%Y-%m-%dT%H:%M') if transaction.created_at else today_datetime }}{% else %}{{ today_datetime }}{% endif %}" required>
                </div>
                
                <div class="flex-grow-1" style="width: 20%;">
                    <label for="transaction-no" class="form-label mb-1">��Ӧ���������:</label>
                    <input type="text" id="transaction-no" name="transaction_no" class="form-control form-control-sm" 
                           value="{{ transaction.transaction_no if edit_mode else transaction_no }}" readonly>
                    <input type="hidden" id="transaction-id" value="{{ transaction.id if edit_mode else '' }}">
                </div>

                <div class="flex-grow-1" style="width: 25%;">
                    <label for="supplier-id" class="form-label mb-1">��Ӧ������:</label>
                    <div class="supplier-search-container" style="position: relative;">
                        <input type="text" id="supplier-search" class="form-control form-control-sm"
                               placeholder="������Ӧ��..." autocomplete="off"
                               value="{% if edit_mode and transaction.supplier_id %}{{ suppliers|selectattr('id', 'equalto', transaction.supplier_id)|first|attr('name') }}{% endif %}">
                        <input type="hidden" id="supplier-id" name="supplier_id"
                               value="{% if edit_mode and transaction.supplier_id %}{{ transaction.supplier_id }}{% endif %}">
                        <div class="supplier-search-results" style="display: none; position: absolute; z-index: 999999; background: white; border: 1px solid #ced4da; border-radius: 4px; box-shadow: 0 4px 12px rgba(0,0,0,0.25); max-height: 200px; overflow-y: auto;"></div>
                    </div>
                </div>
                
                <div class="flex-grow-1" style="width: 35%;">
                    <label for="notes" class="form-label mb-1">��ע:</label>
                    <input type="text" id="notes" name="notes" class="form-control form-control-sm" 
                           value="{{ transaction.notes if edit_mode else '' }}">
                </div>
            </div>
        </div>
    </div>

    <!-- ���Ƿ����ϸ���������ڶ�̬�������֮ǰ -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card" style="height: 400px; border: none;">
                <div class="card-header py-2" style="background-color: #000000; color: white;">
                    <h6 class="mb-0 fw-bold">������ϸ</h6>
                        </div>
                <div class="card-body p-0" style="height: calc(100% - 50px); overflow: hidden; background-color: white;">
                    <div id="supplier-owed-materials">
                        {% if edit_mode and transaction.supplier_id and supplier_old_materials and supplier_old_materials|length > 0 %}
                        <!-- �༭ģʽ��ֱ����ʾԤ����ľ�����ϸ -->
                        <div class="p-2">
                            <div class="simple-table-responsive">
                                <table class="supplier-transaction-table" style="table-layout: fixed; width: 100%;">
                                    <thead>
                                        <tr>
                                            <th style="width: 33.33%;">��������</th>
                                            <th style="width: 33.33%;">����Ƿ��</th>
                                            <th style="width: 33.33%;">���ڴ���</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for material_name, details in supplier_old_materials.items() %}
                                        <tr>
                                            <td>{{ material_name }}</td>
                                            <td class="text-end">{{ "%.2f"|format(details.owed_weight) }}��</td>
                                            <td class="text-end">{{ "%.2f"|format(details.stored_weight) }}��</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% elif edit_mode and transaction.supplier_id %}
                        <!-- �༭ģʽ�µ�û�о�����ϸ���� -->
                        <div class="text-center py-3">
                            <small class="text-muted">�ù�Ӧ�����޾�����ϸ</small>
                        </div>
                        {% else %}
                        <div class="text-center py-3">
                            <small class="text-muted">��ѡ��Ӧ�̲鿴������ϸ</small>
                        </div>
                        {% endif %}
                </div>
            </div>
        </div>
        </div>
        <div class="col-md-9">
            <!-- ��̬������� -->
            <div class="card">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between mb-2">
                        <h6 class="mb-0 fw-bold">������ϸ¼��</h6>
                        <div class="d-flex">
                            <button type="button" class="btn btn-sm btn-outline-secondary me-1" onclick="addRowByType('deposit_material')" style="font-weight: bold;">����</button>
                            <button type="button" class="btn btn-sm btn-outline-success me-1" onclick="addRowByType('buy_material')" style="font-weight: bold;">����</button>
                            <button type="button" class="btn btn-sm btn-outline-primary me-1" onclick="addRowByType('return_material')" style="font-weight: bold;">����</button>
                            <button type="button" class="btn btn-sm btn-outline-info me-1" onclick="addRowByType('store_material')" style="font-weight: bold;">����</button>
                            <button type="button" class="btn btn-sm btn-outline-warning me-1" onclick="addRowByType('return_money')" style="font-weight: bold;">����</button>
                            <button type="button" class="btn btn-sm btn-outline-danger me-1" onclick="addRowByType('deposit_money')" style="font-weight: bold;">���</button>
                    </div>
                    </div>
                    <div id="transactions-scroll-container" style="height: 400px; overflow-y: auto; border: none; background: white;">
                        <div id="transactions-container" style="width: 100%; padding: 0;">
                            {% if edit_mode and transaction %}
                            <!-- �༭ģʽ��ֱ����ʾ���׼�¼��񣬰���ȷ˳�򣺼��� �� ���� �� ���� �� ���� �� ���� �� ��� -->

                            <!-- ���ϼ�¼ -->
                            {% set deposit_materials = transaction.material_transactions|selectattr('deposit_weight')|list %}
                            {% if deposit_materials %}
                            <div class="transaction-row d-flex align-items-center" data-type="deposit_material" style="margin-bottom: 0 !important;">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 30px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">����</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��������</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">���Ͽ���</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">ʵ�ʵ���</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">�������</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��ע</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in deposit_materials %}
                                            <tr style="height: 18px;">
                                                <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
                                                    <select class="form-control form-control-sm old-material-select" name="deposit_material_type[]" onchange="updateCalculations()" style="font-size: 19px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                                                        <option value="">��ѡ��</option>
                                                        <option value="{{ mt.deposit_material_type or '' }}" selected>{{ mt.deposit_material_type or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
                                                    <input type="number" class="form-control form-control-sm deposit-weight" name="deposit_weight[]" step="0.01" value="{{ "%.2f"|format(mt.deposit_weight) if mt.deposit_weight else '' }}" oninput="updateDepositLoss(this)" style="font-size: 19px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
                                                    <input type="number" class="form-control form-control-sm actual-deposit-weight" name="actual_deposit_weight[]" step="0.01" value="{{ "%.2f"|format(mt.actual_deposit_weight) if mt.actual_deposit_weight else '' }}" oninput="updateDepositLoss(this)" style="font-size: 19px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
                                                    <input type="number" class="form-control form-control-sm deposit-loss" name="deposit_loss[]" step="0.01" value="{{ "%.2f"|format(mt.deposit_loss) if mt.deposit_loss else '' }}" readonly style="font-size: 19px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="material_note[]" value="{{ mt.note or '' }}" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)" style="height: 18px; padding: 0 2px; font-size: 10px;">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- ���ϼ�¼ -->
                            {% set buy_money_materials = transaction.money_transactions|selectattr('return_purpose', 'equalto', '����')|list %}
                            {% if buy_money_materials %}
                            <div class="transaction-row d-flex align-items-center" data-type="buy_material">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 30px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">����</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm" style="table-layout: fixed; width: 100%;">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��������</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">���Ͽ���</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">�ϼ�</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">�ܼƽ��</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��ע</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in buy_money_materials %}
                                            <tr style="height: 20px;">
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <select class="form-control form-control-sm old-material-select" name="buy_material_type[]" onchange="updateCalculations()" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                        <option value="">��ѡ��</option>
                                                        <option value="{{ mt.return_material_type or '' }}" selected>{{ mt.return_material_type or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <input type="number" class="form-control form-control-sm buy-weight" name="buy_weight[]" step="0.01" value="{{ "%.2f"|format(mt.return_weight) if mt.return_weight else '' }}" oninput="calculateBuyTotal(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <input type="number" class="form-control form-control-sm material-price" name="material_price[]" step="0.01" value="{{ "%.2f"|format(mt.return_amount / mt.return_weight) if mt.return_weight and mt.return_weight > 0 else '' }}" oninput="calculateBuyTotal(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <input type="number" class="form-control form-control-sm buy-total" name="buy_total[]" step="1" value="{{ "%.2f"|format(mt.return_amount) if mt.return_amount else '' }}" oninput="calculateMaterialPrice(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="buy_note[]" value="{{ mt.note or '' }}" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)" style="height: 18px; padding: 0 2px; font-size: 10px;">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- ���ϼ�¼ -->
                            {% set return_materials = transaction.material_transactions|selectattr('return_weight')|list %}
                            {% if return_materials %}
                            <div class="transaction-row d-flex align-items-center" data-type="return_material" style="margin-bottom: 0 !important;">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 30px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">����</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��������</th>
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">���Ͽ���</th>
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">������Դ</th>
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��ע</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in return_materials %}
                                            <tr style="height: 22px;">
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm old-material-select" name="return_material_type[]" onchange="updateCalculations()">
                                                        <option value="">��ѡ��</option>
                                                        <option value="{{ mt.return_material_type or '' }}" selected>{{ mt.return_material_type or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <input type="number" class="form-control form-control-sm return-weight" name="return_weight[]" step="0.01" value="{{ "%.2f"|format(mt.return_weight) if mt.return_weight else '' }}" oninput="updateCalculations()">
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm return-material-source" name="return_material_source[]" onchange="handleReturnSourceChange(this)">
                                                        <option value="">��ѡ������Դ</option>
                                                        <option value="{{ mt.return_source or '' }}" selected>{{ mt.return_source or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="material_note[]" value="{{ mt.note or '' }}">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}

                            <!-- ���ϼ�¼ -->
                            {% set store_materials = transaction.material_transactions|selectattr('store_weight')|list %}
                            {% if store_materials %}
                            <div class="transaction-row d-flex align-items-center" data-type="store_material" style="margin-bottom: 0 !important;">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 30px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">����</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��������</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">���Ͽ���</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">������Դ</th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��ע</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in store_materials %}
                                            <tr style="height: 22px;">
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm old-material-select" name="store_material_type[]" onchange="updateCalculations()">
                                                        <option value="">��ѡ��</option>
                                                        <option value="{{ mt.store_material_type or '' }}" selected>{{ mt.store_material_type or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <input type="number" class="form-control form-control-sm store-weight" name="store_weight[]" step="0.01" value="{{ "%.2f"|format(mt.store_weight) if mt.store_weight else '' }}" oninput="updateCalculations()">
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm store-material-source" name="store_material_source[]" onchange="updateCalculations()">
                                                        <option value="">��ѡ�������Դ</option>
                                                        <option value="{{ mt.store_source or '' }}" selected>{{ mt.store_source or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="material_note[]" value="{{ mt.note or '' }}">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            </div>
                            {% endif %}

                            <!-- �����¼ -->
                            {% set return_money = [] %}
                            {% for mt in transaction.money_transactions %}
                                {% if mt.return_amount and (not mt.return_weight or mt.return_weight == 0) %}
                                    {% set _ = return_money.append(mt) %}
                                {% endif %}
                            {% endfor %}
                            {% if return_money %}
                            <div class="transaction-row d-flex align-items-center" data-type="return_money" style="margin-bottom: 0 !important;">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 30px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">����</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">������</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">������Դ</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">������;</th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��ע</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in return_money %}
                                            <tr style="height: 22px;">
                                                <td style="width: 16.66% !important;">
                                                    <input type="number" class="form-control form-control-sm return-amount" name="return_amount[]" step="0.01" value="{{ "%.2f"|format(mt.return_amount) if mt.return_amount else '' }}" oninput="updateCalculations()">
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm return-money-source" name="return_money_source[]" onchange="updateCalculations()">
                                                        <option value="">��ѡ�񻹿���Դ</option>
                                                        <option value="{{ mt.return_source or '' }}" selected>{{ mt.return_source or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm return-purpose" name="return_purpose[]" onchange="updateCalculations()">
                                                        <option value="������" {% if mt.return_purpose == '������' or not mt.return_purpose %}selected{% endif %}>������</option>
                                                        <option value="����" {% if mt.return_purpose == '����' %}selected{% endif %}>����</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="money_note[]" value="{{ mt.note or '' }}">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}
                            <!-- ����¼ -->
                            {% set deposit_money = transaction.money_transactions|selectattr('store_amount')|list %}
                            {% if deposit_money %}
                            <div class="transaction-row d-flex align-items-center" data-type="deposit_money" style="margin-bottom: 0 !important;">
                                <div style="width: 60px; text-align: left; padding-right: 10px; padding-left: 30px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">���</strong></div>
                                <div style="width: calc(100% - 70px);">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr style="height: 20px;">
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">�����</th>
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">�����Դ</th>
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                                                <th class="header-store-payment" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��ע</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for mt in deposit_money %}
                                            <tr style="height: 22px;">
                                                <td style="width: 16.66% !important;">
                                                    <input type="number" class="form-control form-control-sm deposit-amount" name="deposit_amount[]" step="0.01" value="{{ "%.2f"|format(mt.store_amount) if mt.store_amount else '' }}" oninput="updateCalculations()">
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <select class="form-control form-control-sm deposit-source" name="deposit_source[]" onchange="updateCalculations()">
                                                        <option value="">��ѡ������Դ</option>
                                                        <option value="{{ mt.store_source or '' }}" selected>{{ mt.store_source or '' }}</option>
                                                    </select>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-none"></div>
                                                </td>
                                                <td style="width: 16.66% !important;">
                                                    <div class="d-flex">
                                                        <input type="text" class="form-control form-control-sm" name="money_note[]" value="{{ mt.note or '' }}">
                                                        <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            {% endif %}



                            {% elif edit_mode %}
                            <!-- �༭ģʽ�µ�û�н��׼�¼ -->
                            <div class="text-center text-muted py-4">
                                <i class="fas fa-info-circle fa-2x mb-2"></i>
                                <p>�ý��׼�¼������ϸ����</p>
                            </div>
                            {% else %}
                            <!-- ����ģʽ����ʾ�����ʾ -->
                            <div class="text-center text-muted py-4" id="initial-placeholder">
                                <i class="fas fa-plus-circle fa-2x mb-2"></i>
                                <p>����Ϸ���ť��ӽ��׼�¼</p>
                                <small>֧�ֻ��ϡ����ϡ����ϡ����ϡ�������Ȳ���</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- ��Ӧ����Ϣ��Ƭ�����ڱ���·��� -->
    <div class="row mb-3">
        <div class="col-md-9">
            <div class="card mb-2">
                <div class="card-body p-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">����Ƿ��:</span>
                                <span id="previous-owed-gold" class="ms-3 text-end">{{ "%.2f"|format(default_previous_balance.previous_owed_gold) }}��</span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">����Ƿ��:</span>
                                <span id="current-owed-gold" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_owed_gold) }}{% else %}0{% endif %}">
                                    {% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_owed_gold) }}��{% else %}0.00��{% endif %}
                                </span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">���ڻ���:</span>
                                <span id="return-owed-gold" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.return_owed_gold) }}{% else %}0{% endif %}">
                                    {% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.return_owed_gold) }}��{% else %}0.00��{% endif %}
                                </span>
                            </div>
                            <div style="min-width: 180px;">
                                <span class="small fw-bold">�ܼ�Ƿ��:</span>
                                <span id="total-owed-gold" class="ms-3 fw-bold text-end" data-value="{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_owed_gold) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_owed_gold) }}{% endif %}">
                                    {% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_owed_gold) }}��{% else %}{{ "%.2f"|format(default_previous_balance.previous_owed_gold) }}��{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-1">
                        <div class="d-flex align-items-center">
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">���ڴ���:</span>
                                <span id="previous-deposit-gold" class="ms-3 text-end">{{ "%.2f"|format(default_previous_balance.previous_deposit_gold) }}��</span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">���ڴ���:</span>
                                <span id="current-deposit-gold" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_deposit_gold) }}{% else %}0{% endif %}">
                                    {% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_deposit_gold) }}��{% else %}0.00��{% endif %}
                                </span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">���ϵֿ�:</span>
                                <span id="withdraw-deposit-gold" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.withdraw_deposit_gold) }}{% else %}0{% endif %}">
                                    {% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.withdraw_deposit_gold) }}��{% else %}0.00��{% endif %}
                                </span>
                            </div>
                            <div style="min-width: 180px;">
                                <span class="small fw-bold">�ܼƴ���:</span>
                                <span id="total-deposit-gold" class="ms-3 fw-bold text-end" data-value="{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_deposit_gold) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_deposit_gold) }}{% endif %}">
                                    {% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_deposit_gold) }}��{% else %}{{ "%.2f"|format(default_previous_balance.previous_deposit_gold) }}��{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-1">
                        <div class="d-flex align-items-center">
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">����Ƿ��:</span>
                                <span id="previous-owed-amount" class="ms-3 text-end">��{{ "%.2f"|format(default_previous_balance.previous_owed_amount) }}</span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">����Ƿ��:</span>
                                <span id="current-owed-amount" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_owed_amount) }}{% else %}0{% endif %}">
                                    ��{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_owed_amount) }}{% else %}0{% endif %}
                                </span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">���ڻ���:</span>
                                <span id="return-owed-amount" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.return_owed_amount) }}{% else %}0{% endif %}">
                                    ��{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.return_owed_amount) }}{% else %}0{% endif %}
                                </span>
                            </div>
                            <div style="min-width: 180px;">
                                <span class="small fw-bold">�ܼ�Ƿ��:</span>
                                <span id="total-owed-amount" class="ms-3 fw-bold text-end" data-value="{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_owed_amount) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_owed_amount) }}{% endif %}">
                                    ��{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_owed_amount) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_owed_amount) }}{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-1">
                        <div class="d-flex align-items-center">
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">���ڴ��:</span>
                                <span id="previous-deposit-amount" class="ms-3 text-end">��{{ "%.2f"|format(default_previous_balance.previous_deposit_amount) }}</span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">���ڴ��:</span>
                                <span id="current-deposit-amount" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_deposit_amount) }}{% else %}0{% endif %}">
                                    ��{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.current_deposit_amount) }}{% else %}0{% endif %}
                                </span>
                            </div>
                            <div class="me-4" style="min-width: 180px;">
                                <span class="small">���ֿ�:</span>
                                <span id="withdraw-deposit-amount" class="ms-3 text-end" data-value="{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.withdraw_deposit_amount) }}{% else %}0{% endif %}">
                                    ��{% if edit_mode and current_period_data %}{{ "%.2f"|format(current_period_data.withdraw_deposit_amount) }}{% else %}0{% endif %}
                                </span>
                            </div>
                            <div style="min-width: 180px;">
                                <span class="small fw-bold">�ܼƴ��:</span>
                                <span id="total-deposit-amount" class="ms-3 fw-bold text-end" data-value="{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_deposit_amount) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_deposit_amount) }}{% endif %}">
                                    ��{% if edit_mode and total_balance_data %}{{ "%.2f"|format(total_balance_data.total_deposit_amount) }}{% else %}{{ "%.2f"|format(default_previous_balance.previous_deposit_amount) }}{% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card h-100">
                <div class="card-body p-2 d-flex align-items-center justify-content-center">
                    <button type="button" id="save-btn" class="btn btn-success me-2" style="font-weight: bold;">����</button>
                    <a href="/supplier_transactions" class="btn btn-secondary" style="font-weight: bold;">����</a>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
// ȫ�ֹ�Ӧ�����ݱ���
window.suppliersData = [];

// ͳһ���������
function handleError(error, context = '����') {
    const message = error.message || '����δ֪����';
    alert(`${context}ʧ��: ${message}`);
}

// ͳһ�ɹ���ʾ����
function showSuccess(message, callback) {
    if (typeof Swal !== 'undefined') {
        Swal.fire({
            icon: 'success',
            title: '�����ɹ�',
            text: message
        }).then(callback);
    } else {
        alert(message);
        if (callback) callback();
    }
}
{% if suppliers and suppliers|length > 0 %}
window.suppliersData = [
    {% for supplier in suppliers %}
    {
        id: {{ supplier.id }},
        name: "{{ supplier.name|replace('"', '\\"') }}",
        owed_gold: {{ supplier.owed_gold or 0 }},
        deposit_gold: {{ supplier.deposit_gold or 0 }},
        owed_amount: {{ supplier.owed_amount or 0 }},
        deposit_amount: {{ supplier.deposit_amount or 0 }},
        contact_person: "{{ supplier.contact_person or '' }}",
        phone: "{{ supplier.phone or '' }}",
        supplier_type: "{{ supplier.supplier_type or '' }}",
        pinyin: "{{ supplier.pinyin or '' }}",
        pinyin_initials: "{{ supplier.pinyin_initials or '' }}"
    }{% if not loop.last %},{% endif %}
    {% endfor %}
];
{% endif %}

// ��Ҫ��ҳ���ʼ������ - ͳһ��DOMContentLoaded�¼�������
document.addEventListener('DOMContentLoaded', function() {
    try {
        // ���ȫ�ִ�����
        window.addEventListener('error', function(event) {
            // ��Ĭ�������
        });

        // ���ȳ�ʼ��ȫ�ֱ���
        initializeGlobalVariables();

        // ��ʼ����������datasetֵ������������
        initializePreviousBalanceDataset();

        // ������ʼ���ܼ���ʾ�������0��ʼ����
        initializeTotalDisplays();

        // ����Ƿ�Ϊ�༭ģʽ
        const isEditMode = document.getElementById('transaction-id').value !== '';

        // ������ʼ��ҳ��������ܣ����ȴ��첽����
        // �༭ģʽ���ӳٸ���ѡ��򣬱��⸲��Ԥ��ֵ
        if (!isEditMode) {
            updateAllSelects();
        }
        setupDataChangeListeners();
        initializePageFeatures();
        if (isEditMode) {
            // �༭ģʽ��Ԥ���ü���״̬��������ʾ�հ�
            presetEditModeDisplay();
            // �������ر༭����
            startLoadEditDataIfNeeded();
        }

        // �ں�̨�첽�������ݣ���������ҳ����ʾ
        Promise.all([
            fetchOldMaterials(),
            fetchAccounts(),
            fetchGoldSuppliers()
        ]).then(() => {
            // ���ݼ�����ɺ󣬸���ѡ�����ȷ����������
            // ���ø��±�־���������¸���
            window.allSelectsUpdated = false;
            updateAllSelects();

            // ������Ǳ༭ģʽ�����ڿ��԰�ȫ�س�ʼ����Ӧ����Ϣ
            if (!isEditMode) {
                const supplierIdInput = document.getElementById('supplier-id');
                if (supplierIdInput && supplierIdInput.value) {
                    updateSupplierInfo();
                }
            }
        }).catch(error => {
            // ���ݼ���ʧ��ʱ�ľ�Ĭ����
        });

        // �󶨱��水ť�¼�
        const saveBtn = document.getElementById('save-btn');
        if (saveBtn) {
            saveBtn.addEventListener('click', function() {
                submitForm();
            });
        }

    } catch (error) {
        handleError(error, 'ҳ���ʼ��');
    }
});

// Ԥ���ñ༭ģʽ��ʾ������հ�״̬����
function presetEditModeDisplay() {
    // ���ؽ��׼�¼�Ŀհ���ʾ
    const initialPlaceholder = document.getElementById('initial-placeholder');
    if (initialPlaceholder) {
        initialPlaceholder.style.display = 'none';
    }

    // �༭ģʽ�²���ʾ����״̬����Ϊ�����Ѿ��ڷ�������Ԥ����
    // ֱ�ӱ��ַ���������Ⱦ��ֵ����
}

// ��ʼ������������ݵ�datasetֵ���ӷ���������Ⱦ��ֵ�л�ȡ��
function initializePreviousBalanceDataset() {
    // �༭ģʽ�£��������ݶ��Ѿ��ڷ�������Ԥ���㲢��Ⱦ
    const isEditMode = document.getElementById('transaction-id').value !== '';

    if (isEditMode) {
        // �༭ģʽ�£�������Ⱦ��data-value�����л�ȡֵ
        const elements = [
            'previous-owed-gold', 'current-owed-gold', 'return-owed-gold', 'total-owed-gold',
            'previous-deposit-gold', 'current-deposit-gold', 'withdraw-deposit-gold', 'total-deposit-gold',
            'previous-owed-amount', 'current-owed-amount', 'return-owed-amount', 'total-owed-amount',
            'previous-deposit-amount', 'current-deposit-amount', 'withdraw-deposit-amount', 'total-deposit-amount'
        ];

        elements.forEach(id => {
            const element = document.getElementById(id);
            if (element && element.dataset.value !== undefined) {
                // �����Ѿ���data-value�����У�������⴦��
                console.log(`�༭ģʽ��${id} ������Ԥ�� = ${element.dataset.value}`);
            }
        });
    } else {
        // ����ģʽ�£�ֻ������������datasetֵ
        const elements = [
            { id: 'previous-owed-gold', dataset: 'previousOwedGold' },
            { id: 'previous-deposit-gold', dataset: 'previousDepositGold' },
            { id: 'previous-owed-amount', dataset: 'previousOwedAmount' },
            { id: 'previous-deposit-amount', dataset: 'previousDepositAmount' }
        ];

        elements.forEach(item => {
            const element = document.getElementById(item.id);
            if (element) {
                // ����ʾ���ı�����ȡ��ֵ�����õ�dataset
                const text = element.textContent || '';
                const match = text.match(/[\d.]+/);
                if (match) {
                    element.dataset[item.dataset] = match[0];
                }
            }
        });
    }
}

// ����Ǳ༭ģʽ����ʼ��������
function startLoadEditDataIfNeeded() {
    const transactionIdInput = document.getElementById('transaction-id');
    if (transactionIdInput && transactionIdInput.value) {
        const transactionId = transactionIdInput.value;


        // ȷ�����б�Ҫ�������Ѽ���
        if (!window.goldSuppliers || !Array.isArray(window.goldSuppliers) || window.goldSuppliers.length === 0) {

            useDefaultSuppliers();
        }

        if (!window.accounts || window.accounts.length === 0) {

        }
        
        fetch(`/api/supplier_transactions/get/${transactionId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP����! ״̬: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (!data || typeof data !== 'object') {
                    throw new Error('API���ص����ݸ�ʽ��Ч');
                }

                if (data.success === true || data.status === 'success') {
                    if (!data.transaction || typeof data.transaction !== 'object') {
                        throw new Error('��������ȱʧ���ʽ��Ч');
                    }

                    // ȷ�������ֶδ���
                    if (!data.transaction.material_transactions) {
                        data.transaction.material_transactions = [];
                    }
                    if (!data.transaction.money_transactions) {
                        data.transaction.money_transactions = [];
                    }

                    // ����loadEditData����
                    try {
                        loadEditData(data.transaction);
                    } catch (error) {
                        handleError(error, '��������');
                    }
                } else {
                    const errorMessage = data.message || '���ؽ�������ʧ��';
                    alert(errorMessage);
                }
            })
            .catch(error => {
                let errorMessage = '���ؽ�������ʧ��';
                if (error.message) {
                    if (error.message.includes('JSON')) {
                        errorMessage = '���������ص����ݸ�ʽ�������������״̬';
                    } else if (error.message.includes('HTTP')) {
                        errorMessage = '��������ʧ�ܣ�������������';
                    } else {
                        errorMessage = '�������: ' + error.message;
                    }
                }
                alert(errorMessage);
            });
        } else {
        // ����Ƿ��б���ı������
        const savedFormData = sessionStorage.getItem('supplierTransactionFormData');
        if (savedFormData) {
            try {
                const formData = JSON.parse(savedFormData);

                // �ָ��������
                if (formData.supplier_id) {
                    document.getElementById('supplier-id').value = formData.supplier_id;
                    updateSupplierInfo();
                }

                if (formData.business_date) {
                    document.getElementById('business-date').value = formData.business_date;
                }

                if (formData.notes) {
                    document.getElementById('notes').value = formData.notes;
                }

                sessionStorage.removeItem('supplierTransactionFormData');
            } catch (e) {
                // ���Խ�������
            }
        }
    }
    
    // ��ʼ����ɺ�ֻ����ʵ�����ݱ仯ʱ�ż���
    // updateCalculations(); // �Ƴ���ʼ��ʱ�ļ��㣬���ⲻ��Ҫ������
}

function initializeDisplayElements() {
    const displayElements = {
        'owed-gold': ['previous', 'current', 'return', 'total'],
        'deposit-gold': ['previous', 'current', 'withdraw', 'total'],
        'owed-amount': ['previous', 'current', 'return', 'total'],
        'deposit-amount': ['previous', 'current', 'withdraw', 'total']
    };

    for (const [type, prefixes] of Object.entries(displayElements)) {
        prefixes.forEach(prefix => {
            const elementId = `${prefix}-${type}`;
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = type.includes('amount') ? '��0.00' : '0.00��';
            }
        });
    }
}

// �޸�updateSupplierInfo����ȷ����ʾ2λС��
function updateSupplierInfo() {
    const supplierIdInput = document.getElementById('supplier-id');
    if (!supplierIdInput) return;

    // ����Ƿ��ڱ༭ģʽ
    const isEditMode = document.getElementById('transaction-id').value !== '';

    try {
        if (supplierIdInput.value) {
            // ��ȫ�ֹ�Ӧ�������л�ȡ��Ӧ����Ϣ
            let supplierData = null;

            // ʹ��ȫ�ֵ�suppliersData����
            if (window.suppliersData && Array.isArray(window.suppliersData)) {
                supplierData = window.suppliersData.find(s => s.id == supplierIdInput.value);
            }

            if (!supplierData) {
                return;
            }

            // �ڱ༭ģʽ�£���ȫ�����Զ��������ݸ��£�ֻ�ڱ༭���ݼ���ʱ����һ���Ը���
            if (isEditMode) {
                updateSupplierOldMaterialsFromCache(supplierData);
                return;
            }

            const businessDate = document.getElementById('business-date')?.value;

            if (businessDate) {
                let queryDateTime = businessDate;

                // ������ģʽ�£�ʹ����΢��һ���ʱ��ȷ�����������ѱ���ĵ���
                if (!isEditMode) {
                    // ����ҵ������ʱ�䣬Ȼ���1����
                    const dateTime = new Date(businessDate);
                    dateTime.setMinutes(dateTime.getMinutes() + 1);

                    const year = dateTime.getFullYear();
                    const month = String(dateTime.getMonth() + 1).padStart(2, '0');
                    const day = String(dateTime.getDate()).padStart(2, '0');
                    const hours = String(dateTime.getHours()).padStart(2, '0');
                    const minutes = String(dateTime.getMinutes()).padStart(2, '0');
                    queryDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;

                }

                // �༭ģʽ��Ҫ�ų���ǰ����ID
                const excludeTransactionId = isEditMode ? getTransactionIdFromUrl() : null;
                loadUnifiedPreviousBalance(supplierData.id, queryDateTime, excludeTransactionId, supplierData.name, supplierData.contact_person);
            } else {
                // ���û��ҵ�����ڣ�ʹ�õ�ǰ����ʱ��
                const now = new Date();

                // ������ģʽ�£�ʹ����΢��һ���ʱ��ȷ�����������ѱ���ĵ���
                // �ڱ༭ģʽ�£�ʹ���ų�����ID�ķ�ʽ
                if (!isEditMode) {
                    // ����ģʽ��ʹ�õ�ǰʱ��+1���ӣ�ȷ�����������ѱ���ĵ���
                    now.setMinutes(now.getMinutes() + 1);
                }

                const year = now.getFullYear();
                const month = String(now.getMonth() + 1).padStart(2, '0');
                const day = String(now.getDate()).padStart(2, '0');
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const todayDateTime = `${year}-${month}-${day}T${hours}:${minutes}`;

                const excludeTransactionId = isEditMode ? getTransactionIdFromUrl() : null;
                loadUnifiedPreviousBalance(supplierData.id, todayDateTime, excludeTransactionId, supplierData.name, supplierData.contact_person);
            }
            // ===========================================================

            // ���۱༭ģʽ��������ģʽ��ѡ��Ӧ�̺���Ҫ���㱾������
            updateElement('current-owed-gold', '0.00��');
            document.getElementById('current-owed-gold').dataset.value = '0.00';

            updateElement('return-owed-gold', '0.00��');
            document.getElementById('return-owed-gold').dataset.value = '0.00';

            updateElement('current-deposit-gold', '0.00��');
            document.getElementById('current-deposit-gold').dataset.value = '0.00';

            updateElement('withdraw-deposit-gold', '0.00��');
            document.getElementById('withdraw-deposit-gold').dataset.value = '0.00';

            updateElement('current-owed-amount', '��0.00');
            document.getElementById('current-owed-amount').dataset.value = '0.00';

            updateElement('return-owed-amount', '��0.00');
            document.getElementById('return-owed-amount').dataset.value = '0.00';

            updateElement('current-deposit-amount', '��0.00');
            document.getElementById('current-deposit-amount').dataset.value = '0.00';

            updateElement('withdraw-deposit-amount', '��0.00');
            document.getElementById('withdraw-deposit-amount').dataset.value = '0.00';

            // �ܼ����ݽ��� updateCalculations �и��� ���� + ���ڼ���

            // ���¾�����ϸ
            updateSupplierOldMaterialsFromCache(supplierData);

        } else {
            // ���û��ѡ��Ӧ�̣�������������ʾԪ��
            initializeDisplayElements();
            // ���þ�����ϸ����
            preloadOldMaterialNames();
        }
        
        // ѡ��Ӧ�̺�ֻ������ʵ�����ݱ仯ʱ�Ÿ��¼���
        // ��������������ͨ��APIֱ�Ӹ��£����ﲻ��Ҫ��������
        // updateCalculations();

    } catch (error) {
        // ����ʱ����Ĭ��ֵ������Ҫ�������
        // updateCalculations(); // �Ƴ�����Ҫ�ļ������
    }
}

function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

// ͳһ��ȡ�������ݣ��������� + ������ϸ��- ������������
async function loadUnifiedPreviousBalance(supplierId, businessDate, excludeTransactionId = null, supplierName = '', contactPerson = '') {
    try {
        // ����Ψһ�������ʶ��
        const requestKey = `unified_${supplierId}_${businessDate}_${excludeTransactionId || 'null'}`;

        // ��ֹ�ظ�����
        if (window.currentUnifiedRequest === requestKey) {
            return;
        }

        // ����Ѿ�����ͬ�����ݣ���������
        if (window.lastUnifiedRequest === requestKey && window.currentPreviousBalance) {
            return;
        }

        // ����ȫ��������ֹ��������ͬʱ������������
        window.previousBalanceUpdating = true;

        window.currentUnifiedRequest = requestKey;

        // ͬʱ��������API
        const [balanceResponse, detailsResponse] = await Promise.all([
            fetch(`/api/supplier_previous_balance/${supplierId}?business_date=${businessDate}${excludeTransactionId ? `&exclude_transaction_id=${excludeTransactionId}` : ''}`),
            fetch(`/api/supplier_details/${supplierId}`)
        ]);

        if (!balanceResponse.ok || !detailsResponse.ok) {
            throw new Error(`API����ʧ��: balance=${balanceResponse.status}, details=${detailsResponse.status}`);
        }

        const [balanceResult, detailsResult] = await Promise.all([
            balanceResponse.json(),
            detailsResponse.json()
        ]);

        if (balanceResult.success && detailsResult.success) {
            const balanceData = balanceResult.data;

            // ֱ�Ӹ��¿���������������ݣ��ο�����ҳ�����ͻ������ʵ�֣�
            const updates = [
                ['previous-owed-gold', balanceData.previous_owed_gold.toFixed(2) + '��'],
                ['previous-deposit-gold', balanceData.previous_deposit_gold.toFixed(2) + '��'],
                ['previous-owed-amount', '��' + balanceData.previous_owed_amount.toFixed(2)],
                ['previous-deposit-amount', '��' + balanceData.previous_deposit_amount.toFixed(2)]
            ];

            // ��������DOMԪ�أ����������ػ�
            updates.forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                    // ͬʱ����datasetֵ���ڼ���
                    const numericValue = parseFloat(value.replace(/[^0-9.-]/g, ''));
                    element.dataset.value = numericValue.toFixed(2);
                }
            });

            // ����ȫ�ֱ���
            window.currentPreviousBalance = balanceData;

            // ͬʱ���¾�����ϸ
            if (detailsResult.details && supplierName) {
                const materialTotals = calculateMaterialTotalsFromDetails(detailsResult.details, detailsResult.materials);
                displayOldMaterialDetailsFromTotals(materialTotals, supplierName, contactPerson);
            }

            // ���������ɲ�����
            window.lastUnifiedRequest = window.currentUnifiedRequest;
            window.currentUnifiedRequest = null;
            window.previousBalanceUpdating = false; // �ͷ���

            // �����Ѿ�ͨ������DOM�����������������ݣ����ﲻ��Ҫ�������
            // ֻ�����н�������ʱ����Ҫ���¼���
            if (document.querySelectorAll('.transaction-row tbody tr').length > 0) {
                updateCalculations();
            }
        } else {
            window.currentUnifiedRequest = null;
            window.previousBalanceUpdating = false; // �ͷ���
            setDefaultPreviousBalance();
        }
    } catch (error) {
        window.currentUnifiedRequest = null;
        window.previousBalanceUpdating = false; // �ͷ���
        setDefaultPreviousBalance();
    }
}

// �ӹ�Ӧ�̿�����ϸAPI��ȡ����������ݣ�����ԭ�������������ط����ã�
async function loadSupplierPreviousBalance(supplierId, businessDate, excludeTransactionId = null) {
    try {
        // ����Ƿ��������������ڸ�����������
        if (window.previousBalanceUpdating) {
            return;
        }

        // ����Ψһ�������ʶ��
        const requestKey = `${supplierId}_${businessDate}_${excludeTransactionId || 'null'}`;

        // ��ֹ�ظ�����
        if (window.currentPreviousBalanceRequest === requestKey) {
            return;
        }

        // ����Ѿ�����ͬ�����ݣ���������
        if (window.lastPreviousBalanceRequest === requestKey && window.currentPreviousBalance) {
            return;
        }
        window.currentPreviousBalanceRequest = requestKey;

        let url = `/api/supplier_previous_balance/${supplierId}?business_date=${businessDate}`;
        if (excludeTransactionId) {
            url += `&exclude_transaction_id=${excludeTransactionId}`;
        }

        const response = await fetch(url);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();

        if (result.success) {
            const data = result.data;

            // ֱ�Ӹ�������������ʾ���ο�����ҳ�����ͻ������ʵ�֣�
            const updates = [
                ['previous-owed-gold', data.previous_owed_gold.toFixed(2) + '��'],
                ['previous-deposit-gold', data.previous_deposit_gold.toFixed(2) + '��'],
                ['previous-owed-amount', '��' + data.previous_owed_amount.toFixed(2)],
                ['previous-deposit-amount', '��' + data.previous_deposit_amount.toFixed(2)]
            ];

            // ��������DOMԪ�أ����������ػ�
            updates.forEach(([id, value]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                    // ͬʱ����datasetֵ���ڼ���
                    const numericValue = parseFloat(value.replace(/[^0-9.-]/g, ''));
                    element.dataset.value = numericValue.toFixed(2);
                }
            });

            // ����ȫ�ֱ�����������ϸʹ��
            window.currentPreviousBalance = data;

            // ���������ɲ�����
            window.lastPreviousBalanceRequest = window.currentPreviousBalanceRequest;
            window.currentPreviousBalanceRequest = null;

            // ͬ�����¾�����ϸ���
            const currentSupplier = getCurrentSupplierData();
            if (currentSupplier) {
                displayOldMaterialDetailsFromBalance(null, currentSupplier.name, currentSupplier.contact_person);
            }

            // ���¼���
            updateCalculations();
        } else {
            // ��������־
            window.currentPreviousBalanceRequest = null;
            // ���APIʧ�ܣ�ʹ��Ĭ��ֵ0
            setDefaultPreviousBalance();
        }
    } catch (error) {
        // ��������־
        window.currentPreviousBalanceRequest = null;
        // ��������ʹ��Ĭ��ֵ0
        setDefaultPreviousBalance();
    }
}



// ��ʼ����������datasetֵ���ӷ���������Ⱦ��ֵ�л�ȡ��
function initializePreviousBalanceDataset() {
    // ��ҳ���л�ȡ����������Ⱦ��Ĭ��ֵ
    const previousOwedGoldElement = document.getElementById('previous-owed-gold');
    const previousDepositGoldElement = document.getElementById('previous-deposit-gold');
    const previousOwedAmountElement = document.getElementById('previous-owed-amount');
    const previousDepositAmountElement = document.getElementById('previous-deposit-amount');

    if (previousOwedGoldElement) {
        const value = parseFloat(previousOwedGoldElement.textContent.replace('��', '')) || 0;
        previousOwedGoldElement.dataset.value = value.toFixed(2);
    }

    if (previousDepositGoldElement) {
        const value = parseFloat(previousDepositGoldElement.textContent.replace('��', '')) || 0;
        previousDepositGoldElement.dataset.value = value.toFixed(2);
    }

    if (previousOwedAmountElement) {
        const value = parseFloat(previousOwedAmountElement.textContent.replace('��', '')) || 0;
        previousOwedAmountElement.dataset.value = value.toFixed(2);
    }

    if (previousDepositAmountElement) {
        const value = parseFloat(previousDepositAmountElement.textContent.replace('��', '')) || 0;
        previousDepositAmountElement.dataset.value = value.toFixed(2);
    }
}

// ��ʼ���ܼ���ʾ�������0��ʼ����������ֵ
function initializeTotalDisplays() {
    // ��ȡ����������Ϊ��ʼ�ܼ�ֵ������ģʽ�£�
    const previousOwedGold = parseFloat(document.getElementById('previous-owed-gold')?.textContent?.replace('��', '')) || 0;
    const previousDepositGold = parseFloat(document.getElementById('previous-deposit-gold')?.textContent?.replace('��', '')) || 0;
    const previousOwedAmount = parseFloat(document.getElementById('previous-owed-amount')?.textContent?.replace('��', '')) || 0;
    const previousDepositAmount = parseFloat(document.getElementById('previous-deposit-amount')?.textContent?.replace('��', '')) || 0;

    // ��ʼ����������Ϊ0���ܼ�Ϊ��������
    const initialUpdates = [
        ['current-owed-gold', '0.00��'],
        ['return-owed-gold', '0.00��'],
        ['total-owed-gold', previousOwedGold.toFixed(2) + '��'],
        ['current-deposit-gold', '0.00��'],
        ['withdraw-deposit-gold', '0.00��'],
        ['total-deposit-gold', previousDepositGold.toFixed(2) + '��'],
        ['return-owed-amount', '��0.00'],
        ['current-owed-amount', '��0.00'],
        ['current-deposit-amount', '��0.00'],
        ['withdraw-deposit-amount', '��0.00'],
        ['total-owed-amount', '��' + previousOwedAmount.toFixed(2)],
        ['total-deposit-amount', '��' + previousDepositAmount.toFixed(2)]
    ];

    // �������ó�ʼֵ����������
    initialUpdates.forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// ����Ĭ�ϵ�������ȫ��Ϊ0��
function setDefaultPreviousBalance() {
    // ����Ƿ�Ϊ�༭ģʽ�������������Ϊ0
    const isEditMode = document.getElementById('transaction-id').value !== '';
    if (isEditMode) {
        return; // �༭ģʽ�±�������ֵ
    }

    updateElement('previous-owed-gold', '0.00��');
    document.getElementById('previous-owed-gold').dataset.value = '0.00';

    updateElement('previous-deposit-gold', '0.00��');
    document.getElementById('previous-deposit-gold').dataset.value = '0.00';

    updateElement('previous-owed-amount', '��0.00');
    document.getElementById('previous-owed-amount').dataset.value = '0.00';

    updateElement('previous-deposit-amount', '��0.00');
    document.getElementById('previous-deposit-amount').dataset.value = '0.00';
}

// ��URL�л�ȡ����ID�����ڱ༭ģʽ��
function getTransactionIdFromUrl() {
    const path = window.location.pathname;
    const match = path.match(/\/supplier_transaction_edit\/(\d+)/);
    return match ? parseInt(match[1]) : null;
}

// ��ʼ��ҵ�����ڱ仯������
function initializeBusinessDateListener() {
    const businessDateInput = document.getElementById('business-date');
    const supplierSelect = document.getElementById('supplier-id');

    if (businessDateInput && supplierSelect) {
        businessDateInput.addEventListener('change', function() {
            const supplierId = supplierSelect.value;
            const businessDate = this.value;

            if (supplierId && businessDate) {
                // �ڱ༭ģʽ�£�����ҵ�����ڱ仯ʱ���������ݸ���
                const isEditMode = window.location.pathname.includes('/edit/');
                if (isEditMode) {
                    return;
                }

                const excludeTransactionId = null; // ����ģʽ����Ҫ�ų�

                // ��ȡ��Ӧ����Ϣ���ھ�����ϸ����
                const currentSupplier = getCurrentSupplierData();
                if (currentSupplier) {
                    loadUnifiedPreviousBalance(supplierId, businessDate, excludeTransactionId, currentSupplier.name, currentSupplier.contact_person);
                } else {
                    loadSupplierPreviousBalance(supplierId, businessDate, excludeTransactionId);
                }
            }
        });
    }
}

// ��URL�л�ȡ����ID
function getTransactionIdFromUrl() {
    const pathParts = window.location.pathname.split('/');
    const editIndex = pathParts.indexOf('edit');
    if (editIndex !== -1 && editIndex + 1 < pathParts.length) {
        return pathParts[editIndex + 1];
    }
    return null;
}

// ��Ӹ��¾�����ϸ�ĺ���
function preloadOldMaterialNames() {
    // ����Ƿ�Ϊ�༭ģʽ������������þ�����ϸ
    const isEditMode = document.getElementById('transaction-id').value !== '';
    if (isEditMode) {
        return; // �༭ģʽ�±�����������
    }

    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (supplierOwedMaterialsDiv) {
        supplierOwedMaterialsDiv.innerHTML = '<div class="text-center py-3"><small class="text-muted">��ѡ��Ӧ�̲鿴������ϸ</small></div>';
    }
}

function updateSupplierOldMaterialsFromCache(supplierData) {
    // �༭ģʽ��ʹ�÷�������Ԥ����ľ�����ϸ������Ҫ�첽����
    const isEditMode = document.getElementById('transaction-id').value !== '';
    if (isEditMode) {
        // �༭ģʽ�¾�����ϸ�Ѿ��ڷ�������Ԥ���㲢��Ⱦ���������
        return;
    }

    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (!supplierOwedMaterialsDiv) return;

    // ���û�о������ݣ���ʾ����ʾ
    if (!supplierData || !supplierData.name) {
        supplierOwedMaterialsDiv.innerHTML = '<div class="text-center py-3"><small class="text-muted">���޾�����ϸ����</small></div>';
        return;
    }

    // ����ģʽ�²���Ҫ�첽��ȡ������ϸ
    // ֱ�Ӵӹ�Ӧ�̿�����ϸ�������������
    displayOldMaterialDetailsFromBalance(null, supplierData.name, supplierData.contact_person);
}

// �Ż��汾��ʹ�������������ͬ���߼����¾�����ϸ�������ظ�����
function updateOldMaterialDetailsFromPreviousBalance(supplierId, supplierName, contactPerson) {
    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (!supplierOwedMaterialsDiv) return;

    // ��ֹ���ִ�еı�־
    if (window.oldMaterialDetailsUpdating) {
        return;
    }
    window.oldMaterialDetailsUpdating = true;

    // ֱ�Ӵӹ�Ӧ�̿�����ϸ�������������
    displayOldMaterialDetailsFromBalance(null, supplierName, contactPerson);

    // �ӳ��ͷű�־�����������ظ�����
    setTimeout(() => {
        window.oldMaterialDetailsUpdating = false;
    }, 100);
}

// ������������ȡ��ǰѡ�еĹ�Ӧ������
function getCurrentSupplierData() {
    const supplierSelect = document.getElementById('supplier');
    if (!supplierSelect || !supplierSelect.value) return null;

    const selectedOption = supplierSelect.options[supplierSelect.selectedIndex];
    if (!selectedOption) return null;

    return {
        id: supplierSelect.value,
        name: selectedOption.dataset.name || selectedOption.text,
        contact_person: selectedOption.dataset.contactPerson || ''
    };
}

// ������������ȡ��ǰ���ݵ�ҵ��ʱ��
function getCurrentBusinessDate() {
    const businessDateInput = document.getElementById('business-date');
    if (businessDateInput && businessDateInput.value) {
        return businessDateInput.value;
    }

    // ���û������ҵ��ʱ�䣬���ؽ��������
    const today = new Date();
    return today.toISOString().split('T')[0];
}

// ������������ȡ��ǰ����ID���༭ģʽ�£�
function getCurrentTransactionId() {
    // ����Ƿ��Ǳ༭ģʽ
    const isEditMode = window.location.pathname.includes('/edit');
    if (!isEditMode) {
        return null;
    }

    // ��URL����ȡ����ID
    const pathParts = window.location.pathname.split('/');
    const editIndex = pathParts.indexOf('edit');
    if (editIndex > 0) {
        return pathParts[editIndex - 1];
    }

    return null;
}

// �Ż��汾���ӹ�Ӧ�̿�����ϸ����㲢��ʾ������ϸ�������ظ�����
function displayOldMaterialDetailsFromBalance(balanceData, supplierName, contactPerson) {
    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (!supplierOwedMaterialsDiv) return;

    // ��ȡ��ǰ��Ӧ��ID
    const supplierIdInput = document.getElementById('supplier-id');
    if (!supplierIdInput || !supplierIdInput.value) {
        displayOldMaterialDetailsDefault(supplierName, contactPerson, {});
        return;
    }

    const supplierId = supplierIdInput.value;

    // ��ֹ�ظ����õı�־
    const callKey = `${supplierId}_${supplierName}_${Date.now()}`;
    if (window.lastOldMaterialDetailsCall === callKey) {
        return;
    }
    window.lastOldMaterialDetailsCall = callKey;

    // �ӹ�Ӧ�̿�����ϸ���ȡ��������
    fetchSupplierMaterialDetailsForOldMaterials(supplierId, supplierName, contactPerson);
}

// �Ż��汾���ӹ�Ӧ�̿�����ϸ���ȡ�����������ݣ������ظ�����
let oldMaterialDetailsCache = new Map();
let oldMaterialDetailsPromises = new Map();

function fetchSupplierMaterialDetailsForOldMaterials(supplierId, supplierName, contactPerson) {
    const cacheKey = `${supplierId}_${getCurrentBusinessDate()}`;

    // ����л��棬ֱ��ʹ��
    if (oldMaterialDetailsCache.has(cacheKey)) {
        const cachedData = oldMaterialDetailsCache.get(cacheKey);
        displayOldMaterialDetailsFromTotals(cachedData, supplierName, contactPerson);
        return;
    }

    // ������������У��ȴ���������
    if (oldMaterialDetailsPromises.has(cacheKey)) {
        oldMaterialDetailsPromises.get(cacheKey).then(materialTotals => {
            displayOldMaterialDetailsFromTotals(materialTotals, supplierName, contactPerson);
        });
        return;
    }

    // �����µ�����
    const promise = fetch(`/api/supplier_details/${supplierId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.details) {
                // ���㵱ǰ�����Ϸ����о��ϵ��ۼ�ֵ
                const materialTotals = calculateMaterialTotalsFromDetails(data.details, data.materials);

                // ������
                oldMaterialDetailsCache.set(cacheKey, materialTotals);

                return materialTotals;
            } else {
                throw new Error('��ȡ��Ӧ�̿�����ϸʧ��');
            }
        })
        .catch(error => {
            console.error('? ��ȡ��Ӧ�̿�����ϸ����:', error);
            // ���ؿ�����
            return {};
        })
        .finally(() => {
            // ���Promise����
            oldMaterialDetailsPromises.delete(cacheKey);
        });

    // ����Promise
    oldMaterialDetailsPromises.set(cacheKey, promise);

    // ִ��������ʾ���
    promise.then(materialTotals => {
        if (Object.keys(materialTotals).length > 0) {
            displayOldMaterialDetailsFromTotals(materialTotals, supplierName, contactPerson);
        } else {
            displayOldMaterialDetailsDefault(supplierName, contactPerson, {});
        }
    });
}

// �����������ӿ�����ϸ���ݼ�������ۼ�ֵ���ۼ�����֮ǰ���ݵľ������ݱ仯��
function calculateMaterialTotalsFromDetails(details, materials) {
    console.log('?? ��ʼ��������ۼ�ֵ���ۼ�����֮ǰ���ݵľ������ݱ仯��...');

    // ��ȡ��ǰ���ݵ�ҵ��ʱ��
    const currentBusinessDate = getCurrentBusinessDate();
    console.log('?? ��ǰ����ҵ��ʱ��:', currentBusinessDate);

    // ��ȡ��ǰ����ID���༭ģʽ�£�
    const currentTransactionId = getCurrentTransactionId();
    console.log('?? ��ǰ����ID:', currentTransactionId);

    // ��ʼ���ۼ�ֵ
    const materialTotals = {};

    // Ϊ���о������ͳ�ʼ���ۼ�ֵ
    if (window.oldMaterials && window.oldMaterials.length > 0) {
        window.oldMaterials.forEach(material => {
            materialTotals[material.name] = {
                owed: 0,
                stored: 0
            };
        });
    }

    // ��ʱ���������м�¼
    const sortedDetails = details.slice().sort((a, b) => {
        if (a.business_time === '��ʼֵ') return -1;
        if (b.business_time === '��ʼֵ') return 1;
        return new Date(a.business_time) - new Date(b.business_time);
    });

    // �ۼ�����֮ǰ���ݵľ������ݱ仯
    for (let i = 0; i < sortedDetails.length; i++) {
        const detail = sortedDetails[i];

        // �����ʼֵ��¼
        if (detail.business_time === '��ʼֵ') {
            console.log('?? �����ʼֵ��¼');
            if (window.oldMaterials && window.oldMaterials.length > 0) {
                window.oldMaterials.forEach(material => {
                    const materialName = material.name;
                    const owedKey = `${materialName}_debt`;
                    const storedKey = `${materialName}_storage`;

                    if (detail[owedKey] !== undefined) {
                        const owedValue = parseFloat(detail[owedKey]) || 0;
                        materialTotals[materialName].owed = owedValue; // ��ʼֱֵ������
                        console.log(`  ${materialName} ��ʼǷ��: ${owedValue}`);
                    }
                    if (detail[storedKey] !== undefined) {
                        const storedValue = parseFloat(detail[storedKey]) || 0;
                        materialTotals[materialName].stored = storedValue; // ��ʼֱֵ������
                        console.log(`  ${materialName} ��ʼ����: ${storedValue}`);
                    }
                });
            }
            continue;
        }

        // ������ǰ���ݣ��༭ģʽ�£�
        if (currentTransactionId && detail.document_id &&
            detail.document_id.toString() === currentTransactionId.toString()) {
            console.log(`?? ������ǰ���ݼ�¼: ${detail.business_time} (ID: ${detail.document_id})`);
            continue;
        }

        // ���ҵ��ʱ���Ƿ��ڵ�ǰ����֮ǰ
        if (currentBusinessDate && detail.business_time) {
            const detailDate = new Date(detail.business_time);
            const currentDate = new Date(currentBusinessDate);

            if (detailDate >= currentDate) {
                console.log(`?? ���ﵱǰ��֮��ļ�¼��ֹͣ�ۼ�: ${detail.business_time}`);
                break;
            }
        }

        console.log(`? �ۼ�֮ǰ���ݼ�¼: ${detail.business_time}`);

        // �ۼӱ仯ֵ���ۼ���API���ص��Ǳ仯ֵ����Ҫ�ۼӣ�
        if (window.oldMaterials && window.oldMaterials.length > 0) {
            window.oldMaterials.forEach(material => {
                const materialName = material.name;
                const debtKey = `${materialName}_debt`;
                const storageKey = `${materialName}_storage`;

                // �ۼӱ仯ֵ
                if (detail.hasOwnProperty(debtKey)) {
                    const debtChange = parseFloat(detail[debtKey]) || 0;
                    materialTotals[materialName].owed += debtChange;
                    console.log(`  ${materialName} Ƿ�ϱ仯: +${debtChange} = ${materialTotals[materialName].owed} (��¼ʱ��: ${detail.business_time})`);
                }

                if (detail.hasOwnProperty(storageKey)) {
                    const storageChange = parseFloat(detail[storageKey]) || 0;
                    materialTotals[materialName].stored += storageChange;
                    console.log(`  ${materialName} ���ϱ仯: +${storageChange} = ${materialTotals[materialName].stored} (��¼ʱ��: ${detail.business_time})`);
                }
            });
        }
    }

    console.log('?? �����ۼ�ֵ������ɣ��ۼ�����֮ǰ���ݵı仯��:', materialTotals);
    return materialTotals;
}

// ���������������ۼ�ֵ��ʾ������ϸ
function displayOldMaterialDetailsFromTotals(materialTotals, supplierName, contactPerson) {
    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (!supplierOwedMaterialsDiv) return;

    console.log('?? ��ʾ������ϸ�������ۼ�ֵ��:', materialTotals);

    // ����HTML
    let html = `
        <div class="p-2">
            <div class="simple-table-responsive">
                <table class="supplier-transaction-table" style="table-layout: fixed; width: 100%;">
                    <thead>
                        <tr>
                            <th style="width: 33.33%;">��������</th>
                            <th style="width: 33.33%;">����Ƿ��</th>
                            <th style="width: 33.33%;">���ڴ���</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    // �������о������ͣ���ʾ�ۼ�ֵ
    if (window.oldMaterials && window.oldMaterials.length > 0) {
        window.oldMaterials.forEach(material => {
            const materialName = material.name;
            const owedAmount = materialTotals[materialName]?.owed || 0;
            const depositAmount = materialTotals[materialName]?.stored || 0;

            // ��ʾ���о������ͣ�����0ֵ
            html += `
                <tr>
                    <td>${materialName}</td>
                    <td class="${owedAmount > 0 ? 'text-danger' : owedAmount < 0 ? 'text-success' : ''}">${owedAmount.toFixed(2)}��</td>
                    <td class="${depositAmount > 0 ? 'text-success' : depositAmount < 0 ? 'text-danger' : ''}">${depositAmount.toFixed(2)}��</td>
                </tr>
            `;
        });
    } else {
        html += `
            <tr>
                <td colspan="3" class="text-center text-muted py-3">
                    �������ݼ�����...
                </td>
            </tr>
        `;
    }

    html += `
                    </tbody>
                </table>
            </div>
            <div class="mt-2 text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    �����������Թ�Ӧ�̿�����ϸ���ۼ�ֵ
                </small>
            </div>
        </div>
    `;

    supplierOwedMaterialsDiv.innerHTML = html;
}

// ����������Ĭ����ʾ����û��ȫ������ʱ��
function displayOldMaterialDetailsDefault(supplierName, contactPerson, basicData) {
    const supplierOwedMaterialsDiv = document.getElementById('supplier-owed-materials');
    if (!supplierOwedMaterialsDiv) return;

    console.log('?? ��ʾ������ϸ��Ĭ��ģʽ��:', basicData);

    // ����HTML
    let html = `
        <div class="p-2">
            <div class="simple-table-responsive">
                <table class="supplier-transaction-table" style="table-layout: fixed; width: 100%;">
                    <thead>
                        <tr>
                            <th style="width: 33.33%;">��������</th>
                            <th style="width: 33.33%;">����Ƿ��</th>
                            <th style="width: 33.33%;">���ڴ���</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    // ��ʾ���о������͵����ݣ�����0ֵ
    if (window.oldMaterials && window.oldMaterials.length > 0) {
        window.oldMaterials.forEach(material => {
            const materialName = material.name;
            let owedAmount = 0;
            let depositAmount = 0;

            // ����Ǿ������ʹ�ô��������
            if (materialName === '�������') {
                owedAmount = basicData.previous_owed_gold || 0;
                depositAmount = basicData.previous_deposit_gold || 0;
            }
            // ��������������ʾΪ0

            html += `
                <tr>
                    <td>${materialName}</td>
                    <td class="${owedAmount > 0 ? 'text-danger' : owedAmount < 0 ? 'text-success' : ''}">${owedAmount.toFixed(2)}��</td>
                    <td class="${depositAmount > 0 ? 'text-success' : depositAmount < 0 ? 'text-danger' : ''}">${depositAmount.toFixed(2)}��</td>
                </tr>
            `;
        });
    } else {
        html += `
            <tr>
                <td colspan="3" class="text-center text-muted py-3">
                    �������ݼ�����...
                </td>
            </tr>
        `;
    }

    html += `
                    </tbody>
                </table>
            </div>
            <div class="mt-2 text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle"></i>
                    �������������һ��
                </small>
            </div>
        </div>
    `;

    supplierOwedMaterialsDiv.innerHTML = html;
}

document.getElementById('save-btn')?.addEventListener('click', async function() {
    try {
        // ������֤ (����)

        // ��ȡ�������ܼ����ݣ�ȥ���λ�����ֵ��
        const totalOwedGold = parseFloat(document.getElementById('total-owed-gold').textContent.replace('��', '')) || 0;
        const totalDepositGold = parseFloat(document.getElementById('total-deposit-gold').textContent.replace('��', '')) || 0;
        const totalOwedAmount = parseFloat(document.getElementById('total-owed-amount').textContent.replace('��', '')) || 0;
        const totalDepositAmount = parseFloat(document.getElementById('total-deposit-amount').textContent.replace('��', '')) || 0;

        // �������ݶ��󣬰������¹�Ӧ����Ϣ������
        const data = {
            supplier_id: parseInt(document.getElementById('supplier-id')?.value),
            business_date: document.getElementById('business-date')?.value,
            transaction_no: document.getElementById('transaction-no')?.value || '',
            notes: document.getElementById('notes')?.value || '',
            material_transactions: [],
            money_transactions: [],
            // ����ܼ��������ڸ��¹�Ӧ����Ϣ
            updated_supplier_info: {
                owed_gold: totalOwedGold,
                deposit_gold: totalDepositGold,
                owed_amount: totalOwedAmount,
                deposit_amount: totalDepositAmount
            },
            // ����˻���������
            account_transactions: []
        };

        // ������֤
        if (!data.supplier_id) {
            alert('��ѡ��Ӧ��');
            return;
        }
        if (!data.business_date) {
            alert('��ѡ��ҵ������');
            return;
        }

        console.log('��ʼ�ռ���������');
        
        // �ռ���������Ϳ�������
        // �������л�ȡ���ݣ��������������ĸ������
        const allTransactionRows = document.querySelectorAll('.transaction-row');
        allTransactionRows.forEach(row => {
            const type = row.getAttribute('data-type');
            const tableRows = row.querySelectorAll('tbody tr');
            
            console.log(`���� ${type} ���͵Ľ����У��ҵ� ${tableRows.length} ��`);
            
            tableRows.forEach((tr, idx) => {
                console.log(`���� ${type} �ĵ� ${idx+1} ��`);
                
                // ��Բ�ͬ�����ռ�����
                if (type === 'return_material') {
                    const returnWeight = parseFloat(tr.querySelector('[name="return_weight[]"]')?.value) || 0;
                    const returnSource = tr.querySelector('[name="return_material_source[]"]')?.value || '';
                    const returnMaterialTypeElement = tr.querySelector('[name="return_material_type[]"]');
                    const returnMaterialType = returnMaterialTypeElement?.value || '';
                    const note = tr.querySelector('[name="material_note[]"]')?.value || '';

                    console.log(`��������: ����=${returnWeight}, ��Դ=${returnSource}, ����=${returnMaterialType}`);
                    console.log(`���Ͼ�������ѡ���:`, returnMaterialTypeElement);
                    console.log(`���Ͼ�������ѡ��������ѡ��:`, Array.from(returnMaterialTypeElement?.options || []).map(opt => `${opt.value}:${opt.text}`));
                    
                    if (returnWeight > 0) {
                data.material_transactions.push({
                    return_weight: returnWeight,
                    return_source: returnSource,
                    return_material_type: returnMaterialType,
                            note: note
                        });
                        
                        // ���������Դ���������Ϲ�Ӧ�̣�����˻����׼�¼
                        if (returnSource !== '' && returnSource !== '���ϵֿ�') {
                            // ����Ƿ��ǽ��Ϲ�Ӧ��
                            const isGoldSupplier = window.goldSuppliers && window.goldSuppliers.some(s => s.name === returnSource);
                            if (isGoldSupplier) {
                                // �ҵ���Ӧ��Ӧ����Ϣ
                                const goldSupplier = window.goldSuppliers.find(s => s.name === returnSource);
                                if (goldSupplier) {
                                    console.log(`����������Ϲ�Ӧ�� ${returnSource} ��Ƿ�ϼ�¼: ${returnWeight}��`);
                                    // ����ֻ��¼��Ϣ��ʵ�ʸ����ں�˽���
                                }
                            }
                        }
                    }
                } else if (type === 'deposit_material') {
                    const depositWeight = parseFloat(tr.querySelector('[name="deposit_weight[]"]')?.value) || 0;
                    const actualDepositWeight = parseFloat(tr.querySelector('[name="actual_deposit_weight[]"]')?.value) || 0;
                    const depositLoss = parseFloat(tr.querySelector('[name="deposit_loss[]"]')?.value) || 0;
                    const depositMaterialType = tr.querySelector('[name="deposit_material_type[]"]')?.value || '';
                    const note = tr.querySelector('[name="material_note[]"]')?.value || '';
                    
                    console.log(`��������: ����=${depositMaterialType}, ����=${depositWeight}, ʵ������=${actualDepositWeight}, ���=${depositLoss}`);
                    
                    if (depositWeight > 0) {
                        data.material_transactions.push({
                            deposit_weight: depositWeight,
                            deposit_material_type: depositMaterialType,
                            actual_deposit_weight: actualDepositWeight,
                            deposit_loss: depositLoss,
                            note: note
                        });
                    }
                } else if (type === 'buy_material') {
                    const materialTypeElement = tr.querySelector('[name="buy_material_type[]"]');
                    const materialType = materialTypeElement?.value || '';
                    const buyWeight = parseFloat(tr.querySelector('[name="buy_weight[]"]')?.value) || 0;
                    const materialPrice = parseFloat(tr.querySelector('[name="material_price[]"]')?.value) || 0;
                    const buyTotal = parseFloat(tr.querySelector('[name="buy_total[]"]')?.value) || 0;
                    const note = tr.querySelector('[name="buy_note[]"]')?.value || '';

                    console.log(`��������: ����=${materialType}, ����=${buyWeight}, ����=${materialPrice}`);
                    console.log(`���Ͼ�������ѡ���:`, materialTypeElement);
                    console.log(`���Ͼ�������ѡ��������ѡ��:`, Array.from(materialTypeElement?.options || []).map(opt => `${opt.value}:${opt.text}`));
                    
                    if (buyWeight > 0) {
                        // ���Ͻ�ת��Ϊ����ף�ͬʱ���в�����Ϣ
                        data.money_transactions.push({
                            return_amount: buyTotal,
                            return_source: '�ֽ�', // ����Ĭ����ԴΪ�ֽ�
                            return_purpose: '����',
                            return_material_type: materialType,
                            return_weight: buyWeight,
                            note: note
                        });
                    }
                } else if (type === 'store_material') {
                    const materialType = tr.querySelector('[name="store_material_type[]"]')?.value || '';
                    const storeWeight = parseFloat(tr.querySelector('[name="store_weight[]"]')?.value) || 0;
                    const storeSource = tr.querySelector('[name="store_material_source[]"]')?.value || '';
                    const note = tr.querySelector('[name="material_note[]"]')?.value || '';
                    
                    console.log(`��������: ����=${materialType}, ����=${storeWeight}, ��Դ=${storeSource}`);
                    
                    if (storeWeight > 0) {
                        data.material_transactions.push({
                            store_weight: storeWeight,
                            store_source: storeSource,
                            store_material_type: materialType,
                            note: note
                        });
                    }
                } else if (type === 'return_money') {
                    const returnAmount = parseFloat(tr.querySelector('[name="return_amount[]"]')?.value) || 0;
                    const returnSource = tr.querySelector('[name="return_money_source[]"]')?.value || '';
                    const returnPurpose = tr.querySelector('[name="return_purpose[]"]')?.value || '������';  // ��ӻ�����;
                    const note = tr.querySelector('[name="money_note[]"]')?.value || '';
                    
                    console.log(`��������: ���=${returnAmount}, ��Դ=${returnSource}, ��;=${returnPurpose}`);
                    
                    if (returnAmount > 0) {
                        data.money_transactions.push({
                            return_amount: returnAmount,
                            return_source: returnSource,
                            return_purpose: returnPurpose,
                            note: note
                        });
                        
                        // ���������Դ���˻��������˻����׼�¼
                        if (returnSource !== '' && returnSource !== '���ֿ�') {
                            // ����Ƿ����˻�
                            const isAccount = window.accounts && window.accounts.some(a => a.name === returnSource);
                            if (isAccount) {
                                console.log(`����˻� ${returnSource} ��֧����¼: ��${returnAmount}`);
                                // ��ȡ��Ӧ����Ϣ�Ա����˻����׼�¼����ʾ
                                const supplierName = document.querySelector('#supplier-id option:checked')?.text || '';
                                
                                data.account_transactions.push({
                                    account_name: returnSource,
                                    amount: -returnAmount,  // ֧��Ϊ����
                                    transaction_type: 'expense',
                                    purpose: returnPurpose === '����' ? '��Ӧ������' : '��Ӧ�̻���',
                                    related_party: supplierName,
                                    notes: note || `֧������Ӧ�� ${supplierName} ��${returnPurpose === '����' ? '���Ͽ�' : '����'}`
                                });
                            }
                        }
                    }
                } else if (type === 'deposit_money') {
                    const storeAmount = parseFloat(tr.querySelector('[name="deposit_amount[]"]')?.value) || 0;
                    const depositType = tr.querySelector('[name="deposit_source[]"]')?.value || '';
                    const note = tr.querySelector('[name="money_note[]"]')?.value || '';
                    
                    console.log(`�������: ���=${storeAmount}, ����=${depositType}`);
                    
                    if (storeAmount > 0) {
                        data.money_transactions.push({
                            store_amount: storeAmount,
                            store_source: depositType,
                            note: note
                        });
                        
                        // ��������Դ���˻��������˻����׼�¼
                        if (depositType !== '') {
                            // ����Ƿ����˻�
                            const isAccount = window.accounts && window.accounts.some(a => a.name === depositType);
                            if (isAccount) {
                                console.log(`����˻� ${depositType} ��֧����¼: ��${storeAmount}`);
                                // ��ȡ��Ӧ����Ϣ�Ա����˻����׼�¼����ʾ
                                const supplierName = document.querySelector('#supplier-id option:checked')?.text || '';
                                
                                data.account_transactions.push({
                                    account_name: depositType,
                                    amount: -storeAmount,  // ֧��Ϊ����
                                    transaction_type: 'expense',
                                    purpose: '��Ӧ�̴��',
                                    related_party: supplierName,
                                    notes: note || `֧������Ӧ�� ${supplierName} �Ĵ��`
                                });
                            }
                        }
                    }
                }
            });
        });
        
        console.log('�ռ����������ܽ�:');
        console.log(`- ���Ͻ���: ${data.material_transactions.length}��`);
        console.log(`- �����: ${data.money_transactions.length}��`);
        console.log(`- �˻�����: ${data.account_transactions.length}��`);
        console.log('�������ݶ���:', data);
        
        // �������ݵ����������༭ģʽ��ʹ��PUT�����������
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const transactionId = document.getElementById('transaction-id').value;
        const url = transactionId 
            ? `/supplier_transactions/${transactionId}/edit`  // �༭ģʽ
            : '/supplier_transactions/add';                  // ����ģʽ
            
        console.log(`ʹ��API: ${url}`);
            
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(data)
        });

        // �Ľ��������Ԥ��JSON��������
        let result;
        try {
            const responseText = await response.text();
            console.log('������ԭʼ��Ӧ:', responseText);
            
            // �����Ӧ�Ƿ�ΪHTML (ͨ����ʾ����������ҳ��)
            if (responseText.trim().startsWith('<')) {
                console.error('������������HTML����JSON�������Ƿ���������');
                throw new Error('������������HTML����ҳ�棬����ϵ����Ա');
            }
            
            // ���Խ���JSON
            try {
                result = JSON.parse(responseText);
            } catch (jsonError) {
                console.error('JSON��������:', jsonError);
                throw new Error('�޷�������������Ӧ������ϵ����Ա');
            }
        } catch (error) {
            handleError(error, '������Ӧ');
            return;
        }

        console.log('��������Ӧ����:', result);
        
        if (result.success) {
            // ����Ƿ����ޱ仯�����
            if (result.no_changes) {
                alert('û�м�⵽���ݱ�����������б�ҳ��');
                window.location.href = '/supplier_transactions';
            } else {
            alert('����ɹ���');
                window.location.href = '/supplier_transactions';
            }
        } else {
            // ����Ƿ��ǽ��ױ���ظ�����
            if (result.message && result.message.includes('���ױ���ظ�')) {
                alert('���ױ���ظ���ϵͳ���Զ�ˢ��ҳ���ȡ�±��');
                // �����û������������
                const formData = {
                    supplier_id: document.getElementById('supplier-id').value,
                    business_date: document.getElementById('business-date').value,
                    notes: document.getElementById('notes').value
                };
                // �����ݴ洢��sessionStorage��
                sessionStorage.setItem('supplierTransactionFormData', JSON.stringify(formData));
                // ˢ��ҳ��
                window.location.reload();
                return;
            }
            
            handleError(new Error(result.message || '����δ֪����'), '����');
        }
    } catch (error) {
        handleError(error, '�������');
    }
});

// ���ȱʧ��handleReturnSourceChange����
function handleReturnSourceChange(selectElement) {
    const row = selectElement.closest('tr');
    if (!row) return;

    const wastageLossInput = row.querySelector('[name="wastage_loss[]"]');

    // ��ļ����Ѽ򻯣�����ʹ��actual_return_weight�ֶ�
    if (wastageLossInput) {
        wastageLossInput.value = '0.00';
    }

    // �������м���
    updateCalculations();
}

// �޸�handleReturnInputChange����ȷ����ʾ2λС��
function handleReturnInputChange(input) {
    const row = input.closest('tr');
    if (!row) return;
    
    const returnWeight = parseFloat(row.querySelector('[name="return_weight[]"]')?.value) || 0;

    // ��ļ����Ѽ򻯣�����ʹ��actual_return_weight�ֶ�
    const wastageLoss = 0;
    const wastageLossInput = row.querySelector('[name="wastage_loss[]"]');
    if (wastageLossInput) {
        wastageLossInput.value = wastageLoss.toFixed(2);
    }
    
    // �����ܼ���
    updateCalculations();
}

// �����ļ��㺯��
function updateWastageLoss(element) {
    const row = element.closest('.transaction-row');
    if (!row) return;
    
    // ���������Ѿ�ɾ����ʵ�ʻ�������������ֶΣ����ﲻ�ٽ�����ؼ���
    // �����������ȷ�������ط��ĵ��ò������
    
    // �����������
    updateCalculations();
}

// ��ָ��˳�������
function insertTableInOrder(container, tableHTML, type) {
    // ���������͵�˳�򣺼��ϡ����ϡ����ϡ����ϡ�������
    const typeOrder = ['deposit_material', 'buy_material', 'return_material', 'store_material', 'return_money', 'deposit_money'];
    const currentTypeIndex = typeOrder.indexOf(type);
    
    if (currentTypeIndex === -1) {
        // ������Ͳ���˳���У�ֱ����ӵ�ĩβ
        container.insertAdjacentHTML('beforeend', tableHTML);
        return;
    }
    
    // ����Ӧ�ò����λ��
    const existingTables = container.querySelectorAll('.transaction-row');
    let insertPosition = null;
    
    for (let i = 0; i < existingTables.length; i++) {
        const existingType = existingTables[i].getAttribute('data-type');
        const existingTypeIndex = typeOrder.indexOf(existingType);
        
        if (existingTypeIndex > currentTypeIndex) {
            insertPosition = existingTables[i];
            break;
        }
    }
    
    if (insertPosition) {
        // ��ָ��λ��֮ǰ����
        insertPosition.insertAdjacentHTML('beforebegin', tableHTML);
    } else {
        // ��ӵ�ĩβ
        container.insertAdjacentHTML('beforeend', tableHTML);
    }
}

// ��� addRowByType �����Ƿ���ڣ���������ڣ������
// �������������Ӳ�ͬ���͵Ľ��ױ����
function addRowByType(type) {
    console.log(`���������: ${type}`);

    // �Ƴ���ʼռλ���ݣ�������ڣ�
    const placeholder = document.getElementById('initial-placeholder');
    if (placeholder) {
        placeholder.remove();
    }

    // �������͵ı���Ƿ��Ѿ�����
    const existingTable = document.querySelector(`.transaction-row[data-type="${type}"]`);

    if (existingTable) {
        // �������Ѿ����ڣ�ֻ�����һ�У�����Ҫ���´������
        const tbody = existingTable.querySelector('tbody');
        if (tbody) {
            let rowContent = '';
            switch(type) {
                case 'return_material':
                    rowContent = createReturnMaterialRow();
                    break;
                case 'deposit_material': // <--- ��Ӽ��� case
                    rowContent = createDepositMaterialRow();
                    break;
                case 'buy_material':
                    rowContent = createBuyMaterialRow();
                    break;
                case 'store_material':
                    rowContent = createStoreMaterialRow();
                    break;
                case 'return_money':
                    rowContent = createReturnMoneyRow();
                    break;
                case 'deposit_money':
                    rowContent = createDepositMoneyRow();
                    break;
            }
            
            if (rowContent) {
                tbody.insertAdjacentHTML('beforeend', rowContent);
            const newRow = tbody.lastElementChild;
                if (newRow) {
                    initializeRowElements(newRow); // Ensure initialization is called
                }
            }
            return; // Exit function after adding row to existing table
        }
    }

    // �����񲻴��ڣ������±��
        const transactionsContainer = document.getElementById('transactions-container');
    if (transactionsContainer) {
        let title = '';
        let theadHtml = '';
        
        // �����������ò�ͬ�ı�ͷ�ͱ���
        switch(type) {
            case 'return_material':
                title = '����';
                theadHtml = `
                    <tr style="height: 20px;">
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��������</th>
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">���Ͽ���</th>
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">������Դ</th>
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;"></th>
                        <th class="header-return-material" style="width: 16.66%; text-align: center; font-size: 22px; font-weight: bold; padding: 2px 1px; border: none;">��ע</th>
                    </tr>
                `;
                break;
            case 'deposit_material':
                title = '����';
                theadHtml = `
                    <tr style="height: 24px;">
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">��������</th>
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">���Ͽ���</th>
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">ʵ�ʵ���</th>
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">�������</th>
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;"></th>
                        <th class="header-store-material" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">��ע</th>
                    </tr>
                `;
                break;
            case 'buy_material':
                title = '����';
                theadHtml = `
                    <tr style="height: 24px;">
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">��������</th>
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">���Ͽ���</th>
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">�ϼ�</th>
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">�ܼƽ��</th>
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;"></th>
                        <th class="header-return-payment" style="width: 16.66%; text-align: center; font-size: 12px; font-weight: 600; padding: 2px 4px; border: none;">��ע</th>
                    </tr>
                `;
                break;
            case 'store_material':
                title = '����';
                theadHtml = `
                    <tr style="height: 18px;">
                        <th class="header-store-material" style="width: 16.66%;">��������</th>
                        <th class="header-store-material" style="width: 16.66%;">���Ͽ���</th>
                        <th class="header-store-material" style="width: 16.66%;">������Դ</th>
                        <th class="header-store-material" style="width: 16.66%;"></th>
                        <th class="header-store-material" style="width: 16.66%;"></th>
                        <th class="header-store-material" style="width: 16.66%;">��ע</th>
                    </tr>
                `;
                break;
            case 'return_money':
                title = '����';
                theadHtml = `
                    <tr style="height: 18px;">
                        <th class="header-return-payment" style="width: 16.66%;">������</th>
                        <th class="header-return-payment" style="width: 16.66%;">������Դ</th>
                        <th class="header-return-payment" style="width: 16.66%;">������;</th>
                        <th class="header-return-payment" style="width: 16.66%;"></th>
                        <th class="header-return-payment" style="width: 16.66%;"></th>
                        <th class="header-return-payment" style="width: 16.66%;">��ע</th>
                    </tr>
                `;
                break;
            case 'deposit_money':
                title = '���';
                theadHtml = `
                    <tr style="height: 18px;">
                        <th class="header-store-payment" style="width: 16.66%;">�����</th>
                        <th class="header-store-payment" style="width: 16.66%;">�����Դ</th>
                        <th class="header-store-payment" style="width: 16.66%;"></th>
                        <th class="header-store-payment" style="width: 16.66%;"></th>
                        <th class="header-store-payment" style="width: 16.66%;"></th>
                        <th class="header-store-payment" style="width: 16.66%;">��ע</th>
                    </tr>
                `;
                break;
        }
        
        // ���������HTML
        const rowHTML = `
        <div class="transaction-row d-flex align-items-center" data-type="${type}" style="margin-bottom: 0 !important;">
                <div style="width: 60px; text-align: left; padding-right: 10px; display: flex; align-items: center; justify-content: flex-start;"><strong class="fs-6">${title}</strong></div>
            <div style="width: calc(100% - 70px);">
                    <table class="table table-sm table-bordered">
                        <thead>
                                ${theadHtml}
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
            </div>
            </div>
        `;

        // �����HTML - ��ָ��˳�����
        insertTableInOrder(transactionsContainer, rowHTML, type);

        // ��ȡ�´����ı����
        const newTableBody = transactionsContainer.querySelector(`.transaction-row[data-type="${type}"] tbody`);

        // ��ӱ��������
        let rowContent = '';
        switch(type) {
            case 'return_material':
                rowContent = createReturnMaterialRow();
                break;
            case 'deposit_material': // <--- ��Ӽ��� case
                rowContent = createDepositMaterialRow();
                break;
            case 'buy_material':
                rowContent = createBuyMaterialRow();
                break;
            case 'store_material':
                rowContent = createStoreMaterialRow();
                break;
            case 'return_money':
                rowContent = createReturnMoneyRow();
                break;
            case 'deposit_money':
                rowContent = createDepositMoneyRow();
                break;
        }

        // ��ӱ����
        if (newTableBody && rowContent) {
            newTableBody.insertAdjacentHTML('beforeend', rowContent);

            // ��ʼ�������е�Ԫ��
            const newRow = newTableBody.querySelector('tr:last-child');
            if (newRow) {
        initializeRowElements(newRow);
            }
        }
    }
}

// ����������
function createReturnMaterialRow() {
    return `
    <tr style="height: 18px;">
        <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
            <select class="form-control form-control-sm old-material-select" name="return_material_type[]" onchange="updateCalculations()" style="font-size: 19px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                <option value="">��ѡ��</option>
            </select>
        </td>
        <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
            <input type="number" class="form-control form-control-sm return-weight" name="return_weight[]" step="0.01" value="" oninput="updateCalculations()" style="font-size: 19px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
        </td>
        <td style="width: 16.66%; text-align: center; padding: 1px; border: none;">
            <select class="form-control form-control-sm return-material-source" name="return_material_source[]" onchange="handleReturnSourceChange(this)" style="font-size: 19px; font-weight: bold; text-align: center; height: 18px; padding: 0 1px; border: none;">
                <option value="">��ѡ������Դ</option>
            </select>
        </td>
        <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
            <div class="d-flex">
                <input type="text" class="form-control form-control-sm" name="material_note[]" oninput="updateCalculations()" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)" style="height: 18px; padding: 0 2px; font-size: 10px;">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </td>
    </tr>`;
}

// ����������
function createBuyMaterialRow() {
    return `
        <tr style="height: 20px;">
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <select class="form-control form-control-sm old-material-select" name="buy_material_type[]" onchange="updateCalculations()" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                    <option value="">��ѡ��</option>
                </select>
            </td>
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <input type="number" class="form-control form-control-sm buy-weight" name="buy_weight[]" step="0.01" oninput="calculateBuyTotal(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
            </td>
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <input type="number" class="form-control form-control-sm material-price" name="material_price[]" step="0.01" oninput="calculateBuyTotal(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
            </td>
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <input type="number" class="form-control form-control-sm buy-total" name="buy_total[]" step="1" oninput="calculateMaterialPrice(this)" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
            </td>
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <div class="d-none"></div>
            </td>
            <td style="width: 16.66%; text-align: center; padding: 1px 2px; border: none;">
                <div class="d-flex">
                    <input type="text" class="form-control form-control-sm" name="buy_note[]" style="font-size: 11px; font-weight: 600; text-align: center; height: 18px; padding: 0 2px;">
                    <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)" style="height: 18px; padding: 0 2px; font-size: 10px;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </td>
        </tr>
    `;
}

// ����������
function createStoreMaterialRow() {
    return `
    <tr style="height: 22px;">
        <td style="width: 16.66%;">
            <select class="form-control form-control-sm old-material-select" name="store_material_type[]" onchange="updateCalculations()">
                <option value="">��ѡ��</option>
            </select>
        </td>
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm store-weight" name="store_weight[]" step="0.01" value="" oninput="updateCalculations()">
        </td>
        <td style="width: 16.66%;">
            <select class="form-control form-control-sm store-material-source" name="store_material_source[]" onchange="updateCalculations()">
                <option value="">��ѡ�������Դ</option>
            </select>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-flex">
                <input type="text" class="form-control form-control-sm" name="material_note[]" oninput="updateCalculations()">
                <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </td>
    </tr>`;
}

// ����������
function createDepositMaterialRow() {
    return `
    <tr style="height: 22px;">
        <td style="width: 16.66%;">
            <select class="form-control form-control-sm old-material-select" name="deposit_material_type[]" onchange="updateCalculations()">
                <option value="">��ѡ��</option>
            </select>
        </td>
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm deposit-weight" name="deposit_weight[]" step="0.01" value="" oninput="updateDepositLoss(this)">
        </td>
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm actual-deposit-weight" name="actual_deposit_weight[]" step="0.01" value="" oninput="updateDepositLoss(this)">
        </td>
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm deposit-loss" name="deposit_loss[]" step="0.01" value="" readonly>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-flex">
                <input type="text" class="form-control form-control-sm" name="material_note[]" oninput="updateCalculations()">
                <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </td>
    </tr>`;
}

// ����������
function createReturnMoneyRow() {
    return `
    <tr style="height: 22px;">
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm return-amount" name="return_amount[]" step="0.01" value="" oninput="updateCalculations()">
            </td>
        <td style="width: 16.66%;">
                <select class="form-control form-control-sm return-money-source" name="return_money_source[]" onchange="updateCalculations()">
                <option value="">��ѡ�񻹿���Դ</option>
                </select>
            </td>
        <td style="width: 16.66%;">
            <select class="form-control form-control-sm return-purpose" name="return_purpose[]" onchange="updateCalculations()">
                <option value="������">������</option>
                <option value="����">����</option>
            </select>
        </td>
        <td style="width: 16.66%;"></td>
        <td style="width: 16.66%;"></td>
        <td style="width: 16.66%;">
                <div class="d-flex">
                <input type="text" class="form-control form-control-sm" name="money_note[]" oninput="updateCalculations()">
                    <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </td>
    </tr>`;
}

// ���������
function createDepositMoneyRow() {
    return `
    <tr style="height: 22px;">
        <td style="width: 16.66%;">
            <input type="number" class="form-control form-control-sm deposit-amount" name="deposit_amount[]" step="0.01" value="" oninput="updateCalculations()">
        </td>
        <td style="width: 16.66%;">
            <select class="form-control form-control-sm deposit-source" name="deposit_source[]" onchange="updateCalculations()">
                <option value="">��ѡ������Դ</option>
            </select>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-none"></div>
        </td>
        <td style="width: 16.66%;">
            <div class="d-flex">
                <input type="text" class="form-control form-control-sm" name="money_note[]" oninput="updateCalculations()">
                <button type="button" class="btn btn-sm btn-danger ms-1" onclick="removeTableRow(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </td>
    </tr>`;
}

// ��ʼ����Ԫ�غ���
function initializeRowElements(row) {
    console.log('��ʼ����Ԫ��');
    if (!row) return;
    
    // ��ȡ�����ڵı������
    const transactionRow = row.closest('.transaction-row');
    if (!transactionRow) return;
    
    const type = transactionRow.getAttribute('data-type');
    console.log('��ʼ��������:', type);
    
    // ��ʼ������ѡ���
    const oldMaterialSelects = row.querySelectorAll('.old-material-select');
    oldMaterialSelects.forEach(select => {
        // ��ʼ������ѡ���
        if (window.oldMaterials) {
            // �������ѡ������һ����ѡ��
            while (select.options.length > 1) {
                select.remove(1);
            }
            
            // ��Ӿ�������ѡ��
            window.oldMaterials.forEach(material => {
                const option = document.createElement('option');
                option.value = material.name;
                option.textContent = material.name;
                select.appendChild(option);
            });
        }
    });
    
    // ���ݲ�ͬ�����ͳ�ʼ���ض�Ԫ��
    if (type === 'return_material') {
        // ��ʼ��������Դ
        const returnMaterialSources = row.querySelectorAll('.return-material-source');
        returnMaterialSources.forEach(select => {
            initializeReturnMaterialSource(select);
        });
    } else if (type === 'store_material') {
        // ��ʼ��������Դ
        const storeMaterialSources = row.querySelectorAll('.store-material-source');
        storeMaterialSources.forEach(select => {
            initializeStoreMaterialSource(select);
        });
    } else if (type === 'return_money') {
        // ��ʼ��������Դ
        const returnMoneySources = row.querySelectorAll('.return-money-source');
        returnMoneySources.forEach(select => {
            initializeReturnMoneySource(select);
        });
        
        // ��ʼ��������;
        const returnPurposes = row.querySelectorAll('[name="return_purpose[]"]');
        returnPurposes.forEach(select => {
            initializeReturnMoneyPurpose(select);
        });
    } else if (type === 'deposit_money') {
        // ��ʼ�������Դ
        const depositSources = row.querySelectorAll('[name="deposit_source[]"]');
        depositSources.forEach(select => {
            initializeDepositMoneySource(select);
        });
    } else if (type === 'deposit_material') {
        // ��ʼ�������ֶ��¼�����
        console.log('��ʼ��������Ԫ��...');
        const depositWeightInputs = row.querySelectorAll('[name="deposit_weight[]"]');
        const actualDepositWeightInputs = row.querySelectorAll('[name="actual_deposit_weight[]"]');
        
        depositWeightInputs.forEach(input => {
            console.log('�󶨼��Ͽ��������¼�');
            input.addEventListener('input', function() {
                updateDepositLoss(this);
            });
            // ȷ����ʼֵΪ���ֶ����ǿ��ַ���
            if (input.value === '' || input.value === '0') {
                input.value = '0';
            }
        });
        
        actualDepositWeightInputs.forEach(input => {
            console.log('��ʵ�ʵ��������¼�');
            input.addEventListener('input', function() {
                updateDepositLoss(this);
            });
            // ȷ����ʼֵΪ���ֶ����ǿ��ַ���
            if (input.value === '' || input.value === '0') {
                input.value = '0';
            }
        });
        
        // ��ʼ���������������У�
        const depositMaterialTypes = row.querySelectorAll('[name="deposit_material_type[]"]');
        depositMaterialTypes.forEach(select => {
            // ��ʼ������ѡ���
            if (window.oldMaterials) {
                updateOldMaterialSelect(select);
            }
        });
        
        // ���δ�������
        if (depositWeightInputs.length > 0) {
            updateDepositLoss(depositWeightInputs[0]);
        }
    }
}

// ɾ������к���
function removeTableRow(button) {
    const row = button.closest('tr');
    if (!row) return;

    const tbody = row.closest('tbody');
    if (!tbody) return;

    // ɾ����
    row.remove();

    // ���û�и����У�ɾ���������
    if (tbody.querySelectorAll('tr').length === 0) {
        const transactionContainer = tbody.closest('.transaction-row');
        if (transactionContainer) {
            transactionContainer.remove();
        }
    }

    // ���¼���
    updateCalculations();
}

// ɾ������������
function removeTransactionRow(button) {
    const transactionRow = button.closest('.transaction-row');
    if (transactionRow) {
        transactionRow.remove();
        // ���¼���
        updateCalculations();
    }
}

// ��������к���
function addMaterialRow(type) {
    let tableBody;
    let rowHTML;
        
        switch(type) {
        case 'return':
            tableBody = document.querySelector('.transaction-row[data-type="return_material"] tbody');
            rowHTML = createReturnMaterialRow();
                break;
        case 'buy':
            tableBody = document.querySelector('.transaction-row[data-type="buy_material"] tbody');
            rowHTML = createBuyMaterialRow();
                break;
        case 'store':
            tableBody = document.querySelector('.transaction-row[data-type="store_material"] tbody');
            rowHTML = createStoreMaterialRow();
                break;
    }
    
    if (tableBody && rowHTML) {
        tableBody.insertAdjacentHTML('beforeend', rowHTML);
        
        // ��ʼ��������е�Ԫ��
        const newRow = tableBody.lastElementChild;
        if (newRow) {
        initializeRowElements(newRow);
    }
    
    // ���¼���
        updateCalculations();
    }
}

// ��ӿ����к���
function addMoneyRow(type) {
    let tableBody;
    let rowHTML;
    
    switch(type) {
        case 'return':
            tableBody = document.querySelector('.transaction-row[data-type="return_money"] tbody');
            rowHTML = createReturnMoneyRow();
                break;
        case 'deposit':
            tableBody = document.querySelector('.transaction-row[data-type="deposit_money"] tbody');
            rowHTML = createDepositMoneyRow();
                break;
        }
        
    if (tableBody && rowHTML) {
        tableBody.insertAdjacentHTML('beforeend', rowHTML);
        
        // ��ʼ��������е�Ԫ��
        const newRow = tableBody.lastElementChild;
        if (newRow) {
            initializeRowElements(newRow);
            
            // ���⴦���Ǯ��Դѡ���
            if (type === 'return') {
                const returnSource = newRow.querySelector('[name="return_money_source[]"]');
                if (returnSource) {
                    initializeReturnMoneySource(returnSource);
                }
            } else if (type === 'deposit') {
                const depositType = newRow.querySelector('[name="deposit_source[]"]');
                if (depositType) {
                    initializeDepositMoneySource(depositType);
                }
            }
    }
    
    // ���¼���
        updateCalculations();
    }
}

// ������������������
function handleReturnInputChange(input) {
    // �Ƴ���ļ����߼����������ܼ���
    updateCalculations();
}

// ������Դ����������
function handleReturnSourceChange(select) {
    // �Ƴ���ʵ�ʻ��Ϻ������صĴ��룬ֻ��������ܼ���
    updateCalculations();
}

// ���ϼ��㺯��
function calculateBuyTotal(input) {
    const row = input.closest('tr');
    if (!row) return;

    const weight = parseFloat(row.querySelector('[name="buy_weight[]"]')?.value || 0);
    const price = parseFloat(row.querySelector('[name="material_price[]"]')?.value || 0);

    if (!isNaN(weight) && !isNaN(price)) {
        const total = weight * price;
        const totalInput = row.querySelector('[name="buy_amount[]"]');
        if (totalInput) {
            totalInput.value = total.toFixed(2);
        }
    }

    // �����ܼ���
    updateCalculations();
}

// �����ܽ����㵥��
function calculateMaterialPrice(input) {
    const row = input.closest('tr');
    if (!row) return;
    
    const total = parseFloat(input.value || 0);
    const weight = parseFloat(row.querySelector('[name="buy_weight[]"]')?.value || 0);
    
    if (!isNaN(total) && !isNaN(weight) && weight > 0) {
        const price = total / weight;
        row.querySelector('[name="material_price[]"]').value = price.toFixed(2);
    }
    
    // �����ܼ���
    updateCalculations();
}

// ��ӳ�ʼ������Ԫ�صĺ���
function initializeNewRowElements(newRow, type) {
    // ��ʼ������ѡ���
    const materialSelects = newRow.querySelectorAll('.old-material-select');
    if (materialSelects.length > 0 && window.oldMaterials) {
        updateOldMaterialSelects(materialSelects);
    }
    
    // ��ʼ���¼�������
    if (type === 'return_material') {
        const returnWeightInput = newRow.querySelector('.return-weight');
        const returnSourceSelect = newRow.querySelector('.return-material-source');

        if (returnWeightInput) {
            returnWeightInput.addEventListener('input', function() { 
                updateCalculations(); 
            });
        }
        
        if (returnSourceSelect) {
            returnSourceSelect.addEventListener('change', function() { 
                handleReturnSourceChange(this); 
            });
        }
        
        // ��ʼ��������Դѡ���
        if (returnSourceSelect) {
            initializeReturnMaterialSource(returnSourceSelect);
        }
    } else if (type === 'buy_material') {
        const buyWeightInput = newRow.querySelector('.buy-weight');
        const materialPriceInput = newRow.querySelector('.material-price');
        
        if (buyWeightInput) {
            buyWeightInput.addEventListener('input', function() {
                updateBuyMaterialTotal(this);
            });
        }
        
        if (materialPriceInput) {
            materialPriceInput.addEventListener('input', function() {
                updateBuyMaterialTotal(this);
            });
        }
    } else if (type === 'store_material') {
        // ��ʼ��������Դ������
        const storeSourceSelect = newRow.querySelector('.store-material-source');
        if (storeSourceSelect) {
            initializeStoreMaterialSource(storeSourceSelect);
        }
    } else if (type === 'return_money') {
        // ��ʼ�������ʽ���Դ������
        const returnMoneySource = newRow.querySelector('.return-money-source');
        if (returnMoneySource && window.accounts) {
            // �������ѡ������һ����ѡ��
            while (returnMoneySource.options.length > 1) {
                returnMoneySource.remove(1);
            }
            
            // ��Ӵ��ֿ۹̶�ѡ��
            const offsetOption = document.createElement('option');
            offsetOption.value = '���ֿ�';
            offsetOption.textContent = '���ֿ�';
            returnMoneySource.appendChild(offsetOption);
            
            // ����˻�ѡ��
            window.accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.name;
                option.textContent = account.name;
                returnMoneySource.appendChild(option);
            });
        }
    } else if (type === 'deposit_money') {
        // ��ʼ������ʽ���Դ������
        const depositTypeSelect = newRow.querySelector('[name="deposit_source[]"]');
        if (depositTypeSelect && window.accounts) {
            // �������ѡ������һ����ѡ��
            while (depositTypeSelect.options.length > 1) {
                depositTypeSelect.remove(1);
            }
            
            // ����˻�ѡ��
            window.accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.name;
                option.textContent = account.name;
                depositTypeSelect.appendChild(option);
            });
        }
    }
}

// �ڱ��水ť����¼�����ӶԹ�Ӧ����Ϣ�ĸ����߼�
// �˴�������ɾ�����Է�ֹ�ظ����¼�������
// document.getElementById('save-btn')?.addEventListener('click', async function() { ... });

// ���fetchOldMaterials����ʵ��
function fetchOldMaterials() {
    if (window.oldMaterials && window.oldMaterials.length > 0) {
        // ����Ѿ��л���ľ������ݣ�ֱ��ʹ��
        updateOldMaterialSelects(document.querySelectorAll('.old-material-select'));
        return Promise.resolve(window.oldMaterials);
    }

    return fetch('/api/old_materials')
        .then(response => {
            if (!response.ok) {
                throw new Error('API����ʧ��: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.success && data.materials) {
                window.oldMaterials = data.materials;
                // console.log('�������ݼ��سɹ�����', data.materials.length, '����¼'); // ����������ע�͵�
                // �������о�������ѡ���
                updateOldMaterialSelects(document.querySelectorAll('.old-material-select'));
                return data.materials;
            } else {
                throw new Error('��ȡ��������ʧ��: ' + (data.message || 'δ֪����'));
            }
        })
        .catch(error => {
            console.error('��ȡ�������ݳ���:', error);
            // ʹ��Ĭ�ϲ����б�
            const defaultMaterials = [
                { name: '�������' },
                { name: '����18K��' },
                { name: '����22K��' },
                { name: '���ϲ���' },
                { name: '������' }
            ];
            window.oldMaterials = defaultMaterials;
            updateOldMaterialSelects(document.querySelectorAll('.old-material-select'));
            return defaultMaterials;
        });
}

// ���¾���ѡ���ĺ���
function updateOldMaterialSelects(selects) {
    if (!window.oldMaterials || !window.oldMaterials.length) return;

    selects.forEach(select => {
        const currentValue = select.value;
        // ����Ƿ���data-original-value���ԣ��༭ģʽ�����õ�ԭʼֵ��
        const originalValue = select.getAttribute('data-original-value');

        // ����Ѿ�����ȷ��ԭʼֵ�����ҵ�ǰֵ�Ѿ���ȷ����������
        if (originalValue && currentValue === originalValue) {
            console.log(`��������ѡ�����£�����ȷ����Ϊ: ${originalValue}`);
            return;
        }

        // �����һ����ѡ��
        while (select.options.length > 1) {
            select.remove(1);
        }

        let hasSelected = false;
        const targetValue = originalValue || currentValue;

        window.oldMaterials.forEach(material => {
            const option = document.createElement('option');
            option.value = material.name;
            option.textContent = material.name;
            // ����ʹ��ԭʼֵ������ǵ�ǰֵ��������Ĭ��ֵ
            if (material.name === targetValue) {
                option.selected = true;
                hasSelected = true;
                console.log(`���þ���ѡ���Ϊ: ${material.name} (��Դ: ${originalValue ? 'ԭʼֵ' : '��ǰֵ'})`);
            } else if (!targetValue && material.name === '�������') {
                // ֻ����û���κ�ֵʱ��Ĭ��ѡ��������
                option.selected = true;
                hasSelected = true;
                console.log(`���þ���ѡ���ΪĬ��ֵ: �������`);
            }
            select.appendChild(option);
        });

        // ���û��ѡ���ѡ���һ�����հ�ѡ�
        if (!hasSelected && select.options.length > 0) {
            select.options[0].selected = true;
        }

        // ǿ������value��ȷ��ѡ����ֵ��ȷ
        if (targetValue) {
            select.value = targetValue;
            console.log(`ǿ�����þ���ѡ���ֵΪ: ${select.value}`);
        }
    });
}

// ���ϼ��㺯��
function calculateBuyTotal(input) {
    const row = input.closest('tr');
    if (!row) return;

    const weight = parseFloat(row.querySelector('[name="buy_weight[]"]')?.value || 0);
    const price = parseFloat(row.querySelector('[name="material_price[]"]')?.value || 0);

    if (!isNaN(weight) && !isNaN(price)) {
        const total = weight * price;
        row.querySelector('[name="buy_total[]"]').value = total.toFixed(2);
    }

    // �����ܼ���
    updateCalculations();
}

// �����ܽ����㵥��
function calculateMaterialPrice(input) {
    const row = input.closest('tr');
    if (!row) return;

    const total = parseFloat(input.value || 0);
    const weight = parseFloat(row.querySelector('[name="buy_weight[]"]')?.value || 0);

    if (!isNaN(total) && !isNaN(weight) && weight > 0) {
        const price = total / weight;
        row.querySelector('[name="material_price[]"]').value = price.toFixed(2);
    }

    // �����ܼ���
    updateCalculations();
}

// ���µ�������ѡ���ĺ���
function updateOldMaterialSelect(select, selectedValue = null) {
    if (!window.oldMaterials || !window.oldMaterials.length || !select) return;

    const currentValue = selectedValue || select.value;
    // �����һ����ѡ��
    while (select.options.length > 1) {
        select.remove(1);
    }

    let hasSelected = false;
    window.oldMaterials.forEach(material => {
        const option = document.createElement('option');
        option.value = material.name;
        option.textContent = material.name;
        // ����ǵ�ǰֵ������'�������'��û�е�ǰֵ����ѡ��
        if (material.name === currentValue || (material.name === '�������' && !currentValue)) {
            option.selected = true;
            hasSelected = true;
        }
        select.appendChild(option);
    });

    // ���û��ѡ���ѡ���һ�����հ�ѡ�
    if (!hasSelected && select.options.length > 0) {
        select.options[0].selected = true;
    }
}

// ��ȡ�˻��б����
function fetchAccounts() {
    if (window.accounts && window.accounts.length > 0) {
        // ������л�����˻����ݣ�ֱ��ʹ��
        updateAccountSelects();
        return Promise.resolve(window.accounts);
    }

    return fetch('/api/accounts')
        .then(response => {
            if (!response.ok) {
                throw new Error('API����ʧ��: ' + response.status);
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                window.accounts = data.data;
                // console.log('�˻����ݼ��سɹ�����', data.data.length, '����¼'); // ����������ע�͵�
                // ���������˻�����ѡ���
                updateAccountSelects();
                return data.data;
            } else {
                throw new Error('��ȡ�˻�����ʧ��: ' + (data.message || 'δ֪����'));
            }
        })
        .catch(error => {
            console.error('��ȡ�˻����ݳ���:', error);
            // ��ʹ��ģ�����ݣ����ֿ�����
            window.accounts = [];
            console.warn('�˻����ݻ�ȡʧ�ܣ�����API�ӿ�');
            return window.accounts;
        });
}

// �����˻�ѡ�����
function updateAccountSelects() {
    // ����ʹ��Ӳ�����Ĭ���˻���ֻʹ�ô����ݿ��ȡ���˻�
    if (!window.accounts || window.accounts.length === 0) {
        console.warn('û���ҵ��˻����ݣ�������Ϊ��');
        return;
    }
    
    // �ֱ������ʹ����ʽ���Դѡ���
    const returnMoneySelects = document.querySelectorAll('.return-money-source');
    const depositTypeSelects = document.querySelectorAll('[name="deposit_source[]"]');
    
    // ���»�����ʽ���Դ�����򣨰������ֿ�ѡ�
    returnMoneySelects.forEach(select => {
        const currentValue = select.value;
        // �������ѡ������һ����ѡ��
        while (select.options.length > 1) {
            select.remove(1);
        }
        
        // ��Ӵ��ֿ۹̶�ѡ��
        const offsetOption = document.createElement('option');
        offsetOption.value = '���ֿ�';
        offsetOption.textContent = '���ֿ�';
        select.appendChild(offsetOption);
        
        // ֻ����˻���Ϣ���е��˻�ѡ��
        window.accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.name;
            option.textContent = account.name;
            select.appendChild(option);
        });
        
        // �����֮ǰѡ�е�ֵ���ָ�ѡ��״̬
        if (currentValue) {
            for (let i = 0; i < select.options.length; i++) {
                if (select.options[i].value === currentValue) {
                     select.options[i].selected = true;
                     break;
                 }
             }
        }
    });
    
    // ���´����ʽ���Դ�����򣨲��������ֿ�ѡ�
    depositTypeSelects.forEach(select => {
        const currentValue = select.value;
        // �������ѡ������һ����ѡ��
        while (select.options.length > 1) {
            select.remove(1);
        }
        
        // ֻ����˻���Ϣ���е��˻�ѡ��
        window.accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.name;
            option.textContent = account.name;
            select.appendChild(option);
        });
        
        // �����֮ǰѡ�е�ֵ���ָ�ѡ��״̬
        if (currentValue) {
            for (let i = 0; i < select.options.length; i++) {
                if (select.options[i].value === currentValue) {
                    select.options[i].selected = true;
                    break;
                }
            }
        }
    });
}

// ��ӻ�ȡ���Ϲ�Ӧ�̵ĺ���
function fetchGoldSuppliers() {
    if (window.goldSuppliers && window.goldSuppliers.length > 0) {
        // ������л������ݣ�ֱ��ʹ��
        updateGoldSupplierSelects();
        updateStoreMaterialSelects();
        return Promise.resolve(window.goldSuppliers);
    }

    // ����ͨ���ض�API��ȡ���Ϲ�Ӧ��
    return fetch('/api/suppliers?type=gold_material', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('���Ϲ�Ӧ��API�����ã�״̬��: ' + response.status);
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success' && data.data && data.data.length > 0) {
            window.goldSuppliers = data.data;
            // console.log('���Ϲ�Ӧ�����ݼ��سɹ�����', data.data.length, '����¼'); // ����������ע�͵�
            // ����������Դѡ���
            updateGoldSupplierSelects();
            updateStoreMaterialSelects();
            return data.data;
        } else {
            throw new Error('û���ҵ����Ϲ�Ӧ��');
        }
    })
    .catch(error => {
        console.warn('���Ϲ�Ӧ��API����ʧ��:', error);
        // ���Ա���API - ��ȡ���й�Ӧ��Ȼ��ɸѡ
        return fetchAllSuppliersAndFilter();
    });
}

// �����й�Ӧ����ɸѡ���Ϲ�Ӧ��
function fetchAllSuppliersAndFilter() {
    console.log('���Դ����й�Ӧ����ɸѡ���Ϲ�Ӧ��...');
    
    try {
        fetch('/api/suppliers', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                console.warn('����APIҲ�����ã�״̬��:', response.status);
                return Promise.reject('����API������');
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success' && data.data && data.data.length > 0) {
                // ɸѡsupplier_typeΪ'gold'��'gold_material'�Ĺ�Ӧ��
                window.goldSuppliers = data.data.filter(supplier => 
                    supplier.supplier_type === 'gold_material' ||
                    supplier.supplier_type === 'gold' ||
                    supplier.supplier_type === 'material' ||
                    supplier.name.includes('��') ||
                    supplier.name.includes('��')
                );
                
                if (window.goldSuppliers.length === 0) {
                    console.warn('û���ҵ�ƥ��Ľ��Ϲ�Ӧ�̣�ʹ�����й�Ӧ��');
                    window.goldSuppliers = data.data;
                }
                
                console.log('�����й�Ӧ����ɸѡ���Ϲ�Ӧ�̳ɹ�:', window.goldSuppliers.length);
                updateGoldSupplierSelects();
                updateStoreMaterialSelects();
                return;
            } else {
                return Promise.reject('û���ҵ���Ӧ������');
            }
        })
        .catch(error => {
            console.warn('����API����ʧ��:', error);
            // ���ʹ�ô�ҳ���ȡ��Ӧ�̵ķ���
            fetchSuppliersFromPage();
        });
    } catch (error) {
        console.error('��ȡ���й�Ӧ��ʧ��:', error);
        // ����ʱ���Դ�ҳ���ȡ
        fetchSuppliersFromPage();
    }
}

// ��ҳ���л�ȡ��Ӧ������
function fetchSuppliersFromPage() {
    console.log('���Դ�ҳ���ȡ��Ӧ������...');
    
    try {
        // �������б��л�ȡ��Ӧ������
        const supplierSelect = document.getElementById('supplier-id');
        if (supplierSelect) {
            const suppliers = [];
            
            // �������б�����ȡ���й�Ӧ��
            Array.from(supplierSelect.options).forEach(option => {
                if (option.value && option.text) {
                    try {
                        // ���Խ���data-info����
                        const supplierData = option.dataset.info ? JSON.parse(option.dataset.info) : null;
                        suppliers.push({
                            id: option.value,
                            name: option.text,
                            ...supplierData
                        });
                    } catch (e) {
                        // ���JSON����ʧ�ܣ�ֻʹ�û�����Ϣ
                        suppliers.push({
                            id: option.value,
                            name: option.text
                        });
                    }
                }
            });
            
            // ɸѡ�������ǽ��Ϲ�Ӧ�̵�ѡ��
            window.goldSuppliers = suppliers.filter(supplier => 
                supplier.supplier_type === 'material' || 
                supplier.name.includes('��') || 
                supplier.name.includes('��')
            );
            
            // ������˺�û�й�Ӧ�̣�ʹ�����й�Ӧ��
            if (window.goldSuppliers.length === 0) {
                console.warn('��ҳ��û���ҵ�ƥ��Ľ��Ϲ�Ӧ�̣�ʹ�����й�Ӧ��');
                window.goldSuppliers = suppliers;
            }
            
            if (window.goldSuppliers.length === 0) {
                // �����Ȼû�����ݣ�ʹ��Ĭ��ֵ
                useDefaultSuppliers();
            } else {
                console.log('��ҳ���ȡ���Ϲ�Ӧ�����ݳɹ�:', window.goldSuppliers.length);
                updateGoldSupplierSelects();
                updateStoreMaterialSelects();
            return;
        }
        } else {
            console.warn('ҳ����û���ҵ���Ӧ�������б�');
            useDefaultSuppliers();
        }
    } catch (error) {
        console.error('��ҳ���ȡ��Ӧ������ʧ��:', error);
        useDefaultSuppliers();
    }
}

// ����ʹ��Ĭ�Ϲ�Ӧ�̣���Ϊ��ʾ������Ϣ
function useDefaultSuppliers() {
    console.error('���Ϲ�Ӧ�����ݻ�ȡʧ�ܣ�����API�ӿ�');
    window.goldSuppliers = [];
    console.warn('���Ϲ�Ӧ������Ϊ�գ������������ѡ��');
}

// ���¾�����Դѡ���
function updateMaterialSourceSelects() {
    // ���»��Ϻʹ�����Դѡ���
    updateGoldSupplierSelects();
    updateStoreMaterialSelects();
    
    console.log('�Ѹ������в�����Դѡ���');
}

// ��ʼ��ҳ���������
function initializeBasicFeatures() {
    // ��ȡҳ��Ԫ��
    const supplierSelect = document.getElementById('supplier-id');
    const businessDateInput = document.getElementById('business-date');
    const transactionNoInput = document.getElementById('transaction-no');

    // ����Ƿ�Ϊ�༭ģʽ
    const isEditMode = {{ 'true' if edit_mode else 'false' }};

    // ����ҵ������Ϊ��ǰ����ʱ�䣨��������ģʽ����Ϊ��ʱ��
    if (businessDateInput && !businessDateInput.value && !isEditMode) {
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const hours = String(today.getHours()).padStart(2, '0');
        const minutes = String(today.getMinutes()).padStart(2, '0');
        businessDateInput.value = `${year}-${month}-${day}T${hours}:${minutes}`;
    }

    // Ϊ��Ӧ������������¼�������
    if (supplierSelect) {
        supplierSelect.addEventListener('change', updateSupplierInfo);

        // ����Ѿ�ѡ���˹�Ӧ�̣����ʼ��ʱ���¹�Ӧ����Ϣ
        if (supplierSelect.value) {
            updateSupplierInfo();
        }
    }
}



// ɾ���ظ���removeTableRow����

// ���updateBuyMaterialTotal��removeRow����
function updateBuyMaterialTotal(input) {
    const row = input.closest('tr');
    if (!row) return;
    
    const buyWeight = parseFloat(row.querySelector('[name="buy_weight[]"]').value) || 0;
    const materialPrice = parseFloat(row.querySelector('[name="material_price[]"]').value) || 0;
    const totalInput = row.querySelector('[name="buy_total[]"]');
    
    if (totalInput) {
        const total = buyWeight * materialPrice;
        totalInput.value = Math.round(total); // ȷ��������
    }
    
    // �����������
    updateCalculations();
}

function removeRow(button) {
    const transactionRow = button.closest('.transaction-row');
    if (transactionRow) {
        // �������������У���ť�ڱ���⣩
        transactionRow.remove();
        updateCalculations();
    } else {
        // �����Ǳ���е�ĳһ��
        const tableRow = button.closest('tr');
        if (tableRow) {
            const tbody = tableRow.closest('tbody');
            tableRow.remove();
            
            // ������û�����ˣ����Ƴ������������
            if (tbody.querySelectorAll('tr').length === 0) {
                const transactionContainer = tbody.closest('.transaction-row');
                if (transactionContainer) {
                    transactionContainer.remove();
                }
            }
            
            updateCalculations();
        }
    }
}

// �Ż��ļ�����º��� - ����DOM��������������
let calculationTimeout = null;
let isCalculating = false;
function updateCalculations() {
    // ������ڼ������ݻ����ڼ����У������ظ�����
    if (window.isLoadingEditData || isCalculating) {
        return;
    }

    // �������������Ƶ������
    if (calculationTimeout) {
        clearTimeout(calculationTimeout);
    }

    calculationTimeout = setTimeout(() => {
        performCalculations();
    }, 50); // 50ms�����ӳ٣����ټ���Ƶ��
}

// ʵ��ִ�м���ĺ��� - �Ż��汾������DOM����
function performCalculations() {
    // ���ü����־����ֹ�ظ�����
    isCalculating = true;

    try {
        // ��ȡ������
        const allRows = document.querySelectorAll('.transaction-row tbody tr');
    
    // ��ʼ��ͳ�Ʊ���
    let totalReturnWeight = 0;         // ����������
    let totalStoreWeight = 0;          // ����������
    let totalOffsetDepositWeight = 0;  // ���ϵֿ�����
    let totalOtherSupplierWeight = 0;  // �������Ϲ�Ӧ�����ӵ�Ƿ��
    let totalDepositWeight = 0;        // ����������
    let totalActualDepositWeight = 0;  // ʵ�ʵ������������Ľ���ֻ���뱾�ڻ��ϣ�
    let totalReturnAmount = 0;         // �����ܽ��
    let totalStoreAmount = 0;          // ����ܽ��
    let totalOffsetDepositAmount = 0;  // ���ֿ۽��
    let totalBuyAmount = 0;            // �����ܽ��
    let totalBuyWeight = 0;            // �������������Ľ���ֻ���뱾�ڻ��ϣ�
    let totalAccountAmount = 0;        // �˻�֧���ܽ��

    // �������н�����
    allRows.forEach(tr => {
        const container = tr.closest('.transaction-row');
        if (!container) return;
        
        const type = container.getAttribute('data-type');
        
// ǰ�˲��� - templates/supplier_transactions/add.html
// �޸Ĵ�������еĴ��벿�� (2450������)
        
        // ���������
        if (type === 'return_material') {
            const returnWeight = parseFloat(tr.querySelector('[name="return_weight[]"]')?.value) || 0;
            const returnSource = tr.querySelector('[name="return_material_source[]"]')?.value || '';
            const returnMaterialType = tr.querySelector('[name="return_material_type[]"]')?.value || '';
            
            // ֻ�о������ż��뻹��������
            if (returnMaterialType === '�������') {
                totalReturnWeight += returnWeight;
            }
            
            // �����Դ��"���ϵֿ�"�Ҿ�������Ϊ"�������"��������ϵֿ�������
            if (returnSource === '���ϵֿ�' && returnMaterialType === '�������') {
                totalOffsetDepositWeight += returnWeight;
            }
            //.�����Դ�ǽ��Ϲ�Ӧ�̣����Ǵ��ϵֿۣ�Ҳ���ǿ�ֵ���������������Ϲ�Ӧ�̵�Ƿ��
            else if (returnSource !== '' && returnSource !== '���ϵֿ�') {
                // ����Ƿ��ǽ��Ϲ�Ӧ��
                const isGoldSupplier = window.goldSuppliers && window.goldSuppliers.some(s => s.name === returnSource);
                if (isGoldSupplier) {
                    // �������ӱ���Ƿ��
                    // totalOtherSupplierWeight += returnWeight;
                    console.log(`�������Ϲ�Ӧ�� ${returnSource} ����: ${returnWeight}�ˣ���Ӱ�챾��Ƿ��`);
                }
            }
        }
        // ���������
        else if (type === 'deposit_material') {
            const depositWeight = parseFloat(tr.querySelector('[name="deposit_weight[]"]')?.value) || 0;
            const actualDepositWeight = parseFloat(tr.querySelector('[name="actual_deposit_weight[]"]')?.value) || 0;
            const depositMaterialType = tr.querySelector('[name="deposit_material_type[]"]')?.value || '';
            
            // ����ʲô���͵ľ��ϣ�����¼����������
            totalDepositWeight += depositWeight;
            
            // ����ʲô���͵ľ��ϣ�����¼ʵ�ʵ���������
            totalActualDepositWeight += actualDepositWeight;
            
            // ��ֻ�о�������ʵ�ʵ��ϲż��뱾�ڻ���
            if (depositMaterialType === '�������') {
                totalReturnWeight += actualDepositWeight;
                console.log(`����������ʵ�ʵ���: ${actualDepositWeight}�ˣ����뱾�ڻ���`);
            } else if (depositMaterialType) {
                console.log(`${depositMaterialType}����ʵ�ʵ���: ${actualDepositWeight}�ˣ������뱾�ڻ���`);
            }
            
            // �������¼��־��ȷ�ϼ��ϼ��ٶ�Ӧ���Ͽ���߼�
            console.log(`���ϲ���: ${depositMaterialType} ${depositWeight}�ˣ����ϲ����ĳ�`);
        }
        // ����������
        else if (type === 'buy_material') {
            const buyWeight = parseFloat(tr.querySelector('[name="buy_weight[]"]')?.value) || 0;
            const buyTotal = parseFloat(tr.querySelector('[name="buy_total[]"]')?.value) || 0;

            // ���ȴ�data-original-value��ȡ�������ͣ�ȷ���༭ģʽ�µ�׼ȷ��
            const materialTypeSelect = tr.querySelector('[name="buy_material_type[]"]');
            const originalValue = materialTypeSelect?.getAttribute('data-original-value');
            const currentValue = materialTypeSelect?.value || '';
            const materialType = originalValue || currentValue;

            console.log(`�����е���: ԭʼֵ='${originalValue}', ��ǰֵ='${currentValue}', ����ֵ='${materialType}'`);

            totalBuyWeight += buyWeight;
            totalBuyAmount += buyTotal;

            // �Ľ���ֻ�о������ż��뱾�ڻ���
            console.log(`������: ��������='${materialType}', ���Ͽ���=${buyWeight}`);
            if (materialType === '�������') {
                totalReturnWeight += buyWeight;
                console.log(`? ����������� ${buyWeight}�� ���뱾�ڻ���`);
            } else if (materialType) {
                console.log(`? ${materialType}���� ${buyWeight}�� �����뱾�ڻ���`);
            }
            
            // �Ľ��������ܽ��ֻ������ϵֿ�
            totalOffsetDepositAmount += buyTotal; 
        }
        // ���������
        else if (type === 'store_material') {
            const storeWeight = parseFloat(tr.querySelector('[name="store_weight[]"]')?.value) || 0;
            const storeMaterialType = tr.querySelector('[name="store_material_type[]"]')?.value || '';
            
            // ֻ�о������ż��뱾�ڴ���
            if (storeMaterialType === '�������') {
                totalStoreWeight += storeWeight;
            }
        }
        // ���������
        else if (type === 'return_money') {
            const returnAmount = parseFloat(tr.querySelector('[name="return_amount[]"]')?.value) || 0;
            const returnSource = tr.querySelector('[name="return_money_source[]"]')?.value || '';
            const returnPurpose = tr.querySelector('[name="return_purpose[]"]')?.value || '';
            
            totalReturnAmount += returnAmount;
            
            // �����Դ��"���ֿ�"��������ֿ��ܽ��
            if (returnSource === '���ֿ�') {
                totalOffsetDepositAmount += returnAmount;
            }
            // �����Դ���˻��������˻�֧���ܽ��
            else if (returnSource !== '' && returnSource !== '���ֿ�') {
                // ����Ƿ����˻�
                const isAccount = window.accounts && window.accounts.some(a => a.name === returnSource);
                if (isAccount) {
                    totalAccountAmount += returnAmount;
                    console.log(`�˻� ${returnSource} ������: ��${returnAmount}`);
                }
            }
            
            // ������������͵Ļ���
            if (returnPurpose === '����') {
                // �������͵Ļ�����ڴ���������ʱ���뻹��������
            }
        }
        // ��������
        else if (type === 'deposit_money') {
            const storeAmount = parseFloat(tr.querySelector('[name="deposit_amount[]"]')?.value) || 0;
            const depositSource = tr.querySelector('[name="deposit_source[]"]')?.value || '';

            totalStoreAmount += storeAmount;

            // �����Դ���˻��������˻�֧���ܽ��
            if (depositSource !== '') {
                // ����Ƿ����˻�
                const isAccount = window.accounts && window.accounts.some(a => a.name === depositSource);
                if (isAccount) {
                    totalAccountAmount += storeAmount;
                    console.log(`�˻� ${depositSource} ������: ��${storeAmount}`);
                }
            }
        }
    });

    // ��ȡ��ʷ����
    const previousOwedGold = parseFloat(document.getElementById('previous-owed-gold').textContent.replace('��', '')) || 0;
    const previousDepositGold = parseFloat(document.getElementById('previous-deposit-gold').textContent.replace('��', '')) || 0;
    const previousOwedAmount = parseFloat(document.getElementById('previous-owed-amount').textContent.replace('��', '')) || 0;
    const previousDepositAmount = parseFloat(document.getElementById('previous-deposit-amount').textContent.replace('��', '')) || 0;

    // ����������ʾ - Ƿ�����
    document.getElementById('return-owed-gold').textContent = totalReturnWeight.toFixed(2) + '��';
    document.getElementById('current-owed-gold').textContent = totalOtherSupplierWeight.toFixed(2) + '��';
    
    // ����������ʾ - �������
    document.getElementById('current-deposit-gold').textContent = totalStoreWeight.toFixed(2) + '��';
    document.getElementById('withdraw-deposit-gold').textContent = totalOffsetDepositWeight.toFixed(2) + '��';
    
    // �����ܼ�����
    const totalOwedGold = previousOwedGold + totalOtherSupplierWeight - totalReturnWeight;
    const totalDepositGold = previousDepositGold + totalStoreWeight - totalOffsetDepositWeight;
    
        // �����ܼƿ���
        const totalOwedAmount = previousOwedAmount - totalReturnAmount; // �Ƴ����Ͻ��
        const totalDepositAmount = previousDepositAmount + totalStoreAmount - totalOffsetDepositAmount; // �Ѱ������Ͻ��

        // ����DOM���� - �ο�����ҳ��ʵ�֣����������ػ�
        const updates = [
            ['current-owed-gold', totalOtherSupplierWeight.toFixed(2) + '��'],
            ['return-owed-gold', totalReturnWeight.toFixed(2) + '��'],
            ['total-owed-gold', totalOwedGold.toFixed(2) + '��'],
            ['current-deposit-gold', totalStoreWeight.toFixed(2) + '��'],
            ['withdraw-deposit-gold', totalOffsetDepositWeight.toFixed(2) + '��'],
            ['total-deposit-gold', totalDepositGold.toFixed(2) + '��'],
            ['return-owed-amount', '��' + totalReturnAmount.toFixed(2)],
            ['current-owed-amount', '��0.00'],
            ['current-deposit-amount', '��' + totalStoreAmount.toFixed(2)],
            ['withdraw-deposit-amount', '��' + totalOffsetDepositAmount.toFixed(2)],
            ['total-owed-amount', '��' + totalOwedAmount.toFixed(2)],
            ['total-deposit-amount', '��' + totalDepositAmount.toFixed(2)]
        ];

        // ʹ���������£�����DOM��������
        updates.forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element && element.textContent !== value) {
                element.textContent = value;
            }
        });
    
    // �Ƴ���ϸ������־�����ٿ���̨����
    // ֻ����Ҫ����ʱ����
    /*
    console.log('����������:');
    console.log('����������:', totalReturnWeight);
    console.log('����������:', totalBuyWeight);
    console.log('����������:', totalDepositWeight);
    console.log('ʵ�ʵ���������:', totalActualDepositWeight);
    console.log('����������:', totalStoreWeight);
    console.log('���ϵֿ�������:', totalOffsetDepositWeight);
    console.log('�������Ϲ�Ӧ������Ƿ��:', totalOtherSupplierWeight);
    console.log('�����ܽ��:', totalReturnAmount);
    console.log('�����ܽ��:', totalBuyAmount);
    console.log('����ܽ��:', totalStoreAmount);
    console.log('���ֿ��ܽ��:', totalOffsetDepositAmount);
    console.log('�˻�֧���ܽ��:', totalAccountAmount);
    */

    } catch (error) {
        console.error('��������г���:', error);
    } finally {
        // �ͷż����־
        isCalculating = false;
    }
}

// ��Ӽ�����ļ��㺯��
function updateDepositLoss(input) {
    const row = input.closest('tr');
    if (!row) return;
    
    const depositWeight = parseFloat(row.querySelector('[name="deposit_weight[]"]')?.value) || 0;
    const actualDepositWeight = parseFloat(row.querySelector('[name="actual_deposit_weight[]"]')?.value) || 0;
    
    // ������� = ���Ͽ��� - ʵ�ʵ�������
    const depositLoss = depositWeight - actualDepositWeight;
    const depositLossInput = row.querySelector('[name="deposit_loss[]"]');
    if (depositLossInput) {
        depositLossInput.value = depositLoss.toFixed(2);
    }
    
    // �����ܼ���
    updateCalculations();
}

// ��ʼ�����¼�������
function initializeRowEventListeners(rowElement) {
    const type = rowElement.getAttribute('data-type');
    
    if (type === 'return_material') {
        const returnWeightInputs = rowElement.querySelectorAll('.return-weight');
        const returnSourceSelects = rowElement.querySelectorAll('.return-material-source');

        returnWeightInputs.forEach(input => {
            input.addEventListener('input', function() { 
                updateCalculations(); 
            });
        });
        
        returnSourceSelects.forEach(select => {
            select.addEventListener('change', function() { 
                handleReturnSourceChange(this); 
            });
        });
    } else if (type === 'buy_material') {
        const buyWeightInputs = rowElement.querySelectorAll('.buy-weight');
        const materialPriceInputs = rowElement.querySelectorAll('.material-price');
        
        buyWeightInputs.forEach(input => {
            input.addEventListener('input', function() {
                updateBuyMaterialTotal(this);
            });
        });
        
        materialPriceInputs.forEach(input => {
            input.addEventListener('input', function() {
                updateBuyMaterialTotal(this);
            });
        });
    } else if (type === 'return_money') {
        // Ϊ�����и����ʽ���Դ������
        const returnMoneySources = rowElement.querySelectorAll('.return-money-source');
        if (returnMoneySources.length > 0 && window.accounts) {
            returnMoneySources.forEach(select => {
                // �������ѡ������һ����ѡ��
                while (select.options.length > 1) {
                    select.remove(1);
                }
                
                // ��Ӵ��ֿ۹̶�ѡ��
                const offsetOption = document.createElement('option');
                offsetOption.value = '���ֿ�';
                offsetOption.textContent = '���ֿ�';
                select.appendChild(offsetOption);
                
                // ����˻�ѡ��
                window.accounts.forEach(account => {
                    const option = document.createElement('option');
                    option.value = account.name;
                    option.textContent = account.name;
                    select.appendChild(option);
                });
            });
        }
    } else if (type === 'deposit_money') {
        // Ϊ����и����ʽ���Դ������
        const depositTypeSelects = rowElement.querySelectorAll('[name="deposit_source[]"]');
        if (depositTypeSelects.length > 0 && window.accounts) {
            depositTypeSelects.forEach(select => {
                // �������ѡ������һ����ѡ��
                while (select.options.length > 1) {
                    select.remove(1);
                }
                
                // ����˻�ѡ��
                window.accounts.forEach(account => {
                    const option = document.createElement('option');
                    option.value = account.name;
                    option.textContent = account.name;
                    select.appendChild(option);
                });
            });
        }
    }
    
    // ��ʼ������ѡ���
    const materialSelects = rowElement.querySelectorAll('.old-material-select');
    if (materialSelects.length > 0 && window.oldMaterials) {
        updateOldMaterialSelects(materialSelects);
    }
    
    // ��ʼ��������Դѡ���
    const materialSourceSelects = rowElement.querySelectorAll('.return-material-source, .store-material-source');
    if (materialSourceSelects.length > 0 && window.goldSuppliers) {
        materialSourceSelects.forEach(select => {
            // ����ǰ�����̶�ѡ��
            let optionsToKeep = 3; // ����ǰ3��ѡ��
            while (select.options.length > optionsToKeep) {
                select.remove(optionsToKeep);
            }
            
            // ��ӽ��Ϲ�Ӧ��
            if (window.goldSuppliers && window.goldSuppliers.length > 0) {
                window.goldSuppliers.forEach(supplier => {
                    const option = document.createElement('option');
                    option.value = supplier.name;
                    option.textContent = supplier.name;
                    select.appendChild(option);
                });
            }
        });
    }
}

// ��ӳ�ʼ����Ԫ�صĺ���
function initializeRowElements(row) {
    if (!row) return;
    
    // ��ȡ�����
    const transactionRow = row.closest('.transaction-row');
    if (!transactionRow) return;
    
    const type = transactionRow.getAttribute('data-type');
    
    // ��ʼ������ѡ��
    const materialSelect = row.querySelector('.old-material-select');
    if (materialSelect && window.oldMaterials) {
        // �������ѡ������һ����ѡ��
        while (materialSelect.options.length > 1) {
            materialSelect.remove(1);
        }
        
        // ��Ӿ���ѡ��
        window.oldMaterials.forEach(material => {
            const option = document.createElement('option');
            option.value = material.name;
            option.textContent = material.name;
            materialSelect.appendChild(option);
        });
        
        // ����Ĭ��ѡ��Ϊ"�������"
        for (let i = 0; i < materialSelect.options.length; i++) {
            if (materialSelect.options[i].value === '�������') {
                materialSelect.selectedIndex = i;
                break;
            }
        }
    }
    
    if (type === 'return_material') {
        // ��ʼ�������е�Ԫ��
        const returnWeightInput = row.querySelector('.return-weight');
        const returnSourceSelect = row.querySelector('.return-material-source');
        
        if (returnWeightInput) {
            returnWeightInput.addEventListener('input', function() {
                handleReturnInputChange(this);
            });
        }
        
        if (returnSourceSelect) {
            returnSourceSelect.addEventListener('change', function() {
                handleReturnSourceChange(this);
            });
            
            // ��ʼ��������Դѡ���
            initializeReturnMaterialSource(returnSourceSelect);
        }
    } else if (type === 'buy_material') {
        // ��ʼ�������е�Ԫ��
        const buyWeightInput = row.querySelector('.buy-weight');
        const materialPriceInput = row.querySelector('.material-price');
        
        if (buyWeightInput) {
            buyWeightInput.addEventListener('input', function() {
                updateBuyMaterialTotal(this);
            });
        }
        
        if (materialPriceInput) {
            materialPriceInput.addEventListener('input', function() {
                updateBuyMaterialTotal(this);
            });
        }
    } else if (type === 'store_material') {
        // ��ʼ�������е�Ԫ��
        const storeSourceSelect = row.querySelector('.store-material-source');
        if (storeSourceSelect) {
            // ��ʼ��������Դѡ���
            initializeStoreMaterialSource(storeSourceSelect);
        }
    } else if (type === 'return_money') {
        const returnMoneySource = row.querySelector('.return-money-source');
        if (returnMoneySource && window.accounts) {
            // �������ѡ������һ����ѡ��
            while (returnMoneySource.options.length > 1) {
                returnMoneySource.remove(1);
            }
            
            // ��Ӵ��ֿ۹̶�ѡ��
            const offsetOption = document.createElement('option');
            offsetOption.value = '���ֿ�';
            offsetOption.textContent = '���ֿ�';
            returnMoneySource.appendChild(offsetOption);
            
            // ����˻�ѡ��
            window.accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.name;
                option.textContent = account.name;
                returnMoneySource.appendChild(option);
            });
        }
    } else if (type === 'deposit_money') {
        const depositTypeSelect = row.querySelector('.deposit-source');
        if (depositTypeSelect && window.accounts) {
            // �������ѡ������һ����ѡ��
            while (depositTypeSelect.options.length > 1) {
                depositTypeSelect.remove(1);
            }
            
            // ����˻�ѡ��
            window.accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.name;
                option.textContent = account.name;
                depositTypeSelect.appendChild(option);
            });
        }
    }
}

// �޸ı���ύ�¼�������������
function submitForm() {
    const isEditMode = document.getElementById('transaction-id').value !== '';
    
    // ����Ǳ༭ģʽ����Ҫ���ԭʼ���ݻع���Ϣ
    if (isEditMode && window.originalFormData) {
        // ��������ǽ�ԭʼ����һ�����͸���ˣ���˻��Ȼع�ԭʼ���ݣ���Ӧ��������
        // ��������ȷ�����ݼ������ȷ��
        
        // ��ȡ����¼���б������
        const formData = logTableData();
        
        // ��ȡ������Ҫ��Ϣ
        const supplierId = document.getElementById('supplier_id').value;
        const businessDate = document.getElementById('business_date').value;
        const note = document.getElementById('notes').value;
        
        // �����ύ���ݶ���
        const data = {
            supplier_id: supplierId,
            business_date: businessDate,
            notes: note,
            material_transactions: [],
            money_transactions: [],
            is_edit_mode: true,
            original_data: window.originalFormData, // ���ԭʼ����
            
            // ��ӱ������
            material_transactions: [
                ...formData.return_material.map(item => ({
                    return_weight: item.return_weight,
                    return_material_type: item.material_type,
                    return_source: item.material_source,
                    wastage_loss: item.wastage_loss,
                    note: item.note
                })),
                ...formData.store_material.map(item => ({
                    store_weight: item.store_weight,
                    store_material_type: item.material_type,
                    store_source: item.material_source,
                    note: item.note
                })),
                ...formData.deposit_material.map(item => ({
                    deposit_weight: item.deposit_weight,
                    deposit_material_type: item.material_type,
                    actual_deposit_weight: item.actual_deposit_weight,
                    deposit_loss: item.deposit_loss,
                    note: item.note
                }))
            ],
            money_transactions: [
                ...formData.return_money.map(item => ({
                    return_amount: item.return_amount,
                    return_source: item.return_money_source,
                    return_purpose: item.return_purpose || '����',
                    note: item.note
                })),
                ...formData.deposit_money.map(item => ({
                    store_amount: item.store_amount,
                    store_source: item.store_source,
                    note: item.note
                })),
                ...formData.buy_material.map(item => ({
                    return_amount: item.buy_total,
                    return_source: '',
                    return_purpose: '����',
                    return_material_type: item.material_type,
                    return_weight: item.buy_weight,
                    note: item.note
                }))
            ]
        };
        
        // �������ݵ�������
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const url = `/supplier_transactions/${document.getElementById('transaction-id').value}/edit`;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('������Ӧ������');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: '����ɹ�',
                    text: '��Ӧ��������¼�Ѹ���'
                }).then(() => {
                    window.location.href = '/supplier_transactions';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '����ʧ��',
                    text: data.message || '����δ֪����'
                });
            }
        })
        .catch(error => {
            console.error('�ύ����:', error);
            Swal.fire({
                icon: 'error',
                title: '�ύ����',
                text: error.message
            });
        });
    } else {
        // ����ģʽ��ʹ��ԭ���߼�
        // ��ȡ����¼���б������
        const formData = logTableData();
        
        // ��ȡ������Ҫ��Ϣ
        const supplierId = document.getElementById('supplier_id').value;
        const businessDate = document.getElementById('business_date').value;
        const note = document.getElementById('notes').value;
        
        // �����ύ���ݶ���
        const data = {
            supplier_id: supplierId,
            business_date: businessDate,
            notes: note,
            material_transactions: [],
            money_transactions: [],
            
            // ��ӱ������
            material_transactions: [
                ...formData.return_material.map(item => ({
                    return_weight: item.return_weight,
                    return_material_type: item.material_type,
                    return_source: item.material_source,
                    wastage_loss: item.wastage_loss,
                    note: item.note
                })),
                ...formData.store_material.map(item => ({
                    store_weight: item.store_weight,
                    store_material_type: item.material_type,
                    store_source: item.material_source,
                    note: item.note
                })),
                ...formData.deposit_material.map(item => ({
                    deposit_weight: item.deposit_weight,
                    deposit_material_type: item.material_type,
                    actual_deposit_weight: item.actual_deposit_weight,
                    deposit_loss: item.deposit_loss,
                    note: item.note
                }))
            ],
            money_transactions: [
                ...formData.return_money.map(item => ({
                    return_amount: item.return_amount,
                    return_source: item.return_money_source,
                    return_purpose: item.return_purpose || '����',
                    note: item.note
                })),
                ...formData.deposit_money.map(item => ({
                    store_amount: item.store_amount,
                    store_source: item.store_source,
                    note: item.note
                })),
                ...formData.buy_material.map(item => ({
                    return_amount: item.buy_total,
                    return_source: '',
                    return_purpose: '����',
                    return_material_type: item.material_type,
                    return_weight: item.buy_weight,
                    note: item.note
                }))
            ]
        };
        
        // �������ݵ�������
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const url = '/supplier_transactions/add';
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('������Ӧ������');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: '����ɹ�',
                    text: '��Ӧ��������¼�ѱ���'
                }).then(() => {
                    window.location.href = '/supplier_transactions';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '����ʧ��',
                    text: data.message || '����δ֪����'
                });
            }
        })
        .catch(error => {
            console.error('�ύ����:', error);
            Swal.fire({
                icon: 'error',
                title: '�ύ����',
                text: error.message
            });
        });
    }
    
    return false; // ��ֹ���Ĭ���ύ
}

// ��ӱ༭ģʽ���ݼ��غ������ѷ�����ʹ��������ع��汾��
function loadEditDataOld(transactionData) {
    console.log('�����˷�����loadEditData��������ʹ���°汾');
    return loadEditData(transactionData);
}

// �ع�loadEditData��������һ�μ����д������б��
function loadEditData(transactionData) {
    try {
        if (!transactionData) {
            console.error('û���ṩ��������');
            return;
        }

        // ��֤transactionData��һ������
        if (typeof transactionData !== 'object') {
            console.error('�������ݸ�ʽ��Ч:', typeof transactionData);
            return;
        }

        console.log('���ر༭����:', transactionData);
    
    // ��齻���������Ƿ������������ֶ�
    console.log('��齻�������е���������ֶ�:');
    console.log('previous_owed_gold =', transactionData.previous_owed_gold);
    console.log('previous_deposit_gold =', transactionData.previous_deposit_gold);
    console.log('previous_owed_amount =', transactionData.previous_owed_amount);
    console.log('previous_deposit_amount =', transactionData.previous_deposit_amount);
    
    // ��ֹ���ܵ��ظ�����
    if (window.editDataLoaded) {
        console.log('�༭�����Ѿ����ع��������ظ�����');
        return;
    }
    window.editDataLoaded = true;
    
    // ���ü������ݱ�־
    window.hasValidDepositData = false;
    
    // ���ػ�����Ϣ
    if (transactionData.supplier_id) {
        // ѡ��Ӧ��
        const supplierSelect = document.getElementById('supplier-id');
        if (supplierSelect) {
            supplierSelect.value = transactionData.supplier_id;
            
            // === �༭ģʽ��ʹ�÷�������Ԥ��������ݣ������첽���� ===
            console.log('�༭ģʽ��ʹ�÷�������Ԥ��������ݣ������첽����');

            // ��ʼ����������datasetֵ���ӷ���������Ⱦ��ֵ�л�ȡ��
            initializePreviousBalanceDataset();

            // �༭ģʽ���������ݶ��Ѿ��ڷ�������Ԥ���㲢��Ⱦ�������ٴμ���
            console.log('�༭ģʽ������������Ԥ��Ⱦ�������ͻ��˼���');
            
            // ��������¼�������������Ӧ����Ϣ���༭ģʽ�²������������
            const event = new Event('change');
            supplierSelect.dispatchEvent(event);
        }
    }
    
    // ����ҵ������ - �༭ģʽ��ʹ��created_at�ֶΣ�����ʱ�䣩
    if (transactionData.created_at) {
        // ��created_atת��Ϊdatetime-local��ʽ
        const createdAt = new Date(transactionData.created_at);
        const year = createdAt.getFullYear();
        const month = String(createdAt.getMonth() + 1).padStart(2, '0');
        const day = String(createdAt.getDate()).padStart(2, '0');
        const hours = String(createdAt.getHours()).padStart(2, '0');
        const minutes = String(createdAt.getMinutes()).padStart(2, '0');
        const datetimeLocal = `${year}-${month}-${day}T${hours}:${minutes}`;

        document.getElementById('business-date').value = datetimeLocal;
        console.log('?? ���ñ༭ģʽҵ������ʱ��:', datetimeLocal);
    } else if (transactionData.business_date) {
        // ���û��created_at��ʹ��business_date + ��ǰʱ��
        const businessDate = new Date(transactionData.business_date + 'T00:00:00');
        const now = new Date();
        businessDate.setHours(now.getHours());
        businessDate.setMinutes(now.getMinutes());

        const year = businessDate.getFullYear();
        const month = String(businessDate.getMonth() + 1).padStart(2, '0');
        const day = String(businessDate.getDate()).padStart(2, '0');
        const hours = String(businessDate.getHours()).padStart(2, '0');
        const minutes = String(businessDate.getMinutes()).padStart(2, '0');
        const datetimeLocal = `${year}-${month}-${day}T${hours}:${minutes}`;

        document.getElementById('business-date').value = datetimeLocal;
        console.log('?? ���ñ༭ģʽҵ������ʱ�䣨ʹ��business_date��:', datetimeLocal);
    }
    
    // ���ؽ��ױ��
    if (transactionData.transaction_no) {
        document.getElementById('transaction-no').value = transactionData.transaction_no;
    }
    
    // ���ر�ע
    if (transactionData.notes) {
        document.getElementById('notes').value = transactionData.notes;
    }
    
    // �༭ģʽ�½��׼�¼�Ѿ��ڷ���������Ⱦ���������¼���
    console.log('�༭ģʽ�����׼�¼����ģ����Ԥ��Ⱦ��������̬����');

    // ֱ�ӷ��أ�����ִ�к����Ķ�̬�����߼�
    return;
    
    // ������������� - �����ͷ��鴴����ͬ���
    let returnMaterialItems = [];
    let buyMaterialItems = [];
    let storeMaterialItems = [];
    let depositMaterialItems = []; // ��Ӽ�����Ŀ����
    
    // ȷ�������ֶδ���
    if (!transactionData.material_transactions) {
        transactionData.material_transactions = [];
    }
    if (!transactionData.money_transactions) {
        transactionData.money_transactions = [];
    }

    // �� material_transactions ����ȡ����
    if (Array.isArray(transactionData.material_transactions) && transactionData.material_transactions.length > 0) {
        transactionData.material_transactions.forEach(item => {
            console.log('�������Ͻ�����:', item);
            // ����
            if (parseFloat(item.return_weight || 0) > 0 && !item.material_price) {
                returnMaterialItems.push(item);
            }

            // ɾ����material_transactions��ȡ�������ݵ��߼�

            // ����
            if (parseFloat(item.store_weight || 0) > 0) {
                storeMaterialItems.push(item);
            }

            // �������� - �ϸ���ֵ�������0
            const depositWeight = parseFloat(item.deposit_weight || 0);
            const actualDepositWeight = parseFloat(item.actual_deposit_weight || 0);
            if (depositWeight > 0 || actualDepositWeight > 0) {
                console.log('�ҵ���Ч��������:', {
                    type: item.deposit_material_type,
                    weight: depositWeight,
                    actual: actualDepositWeight
                });
                    depositMaterialItems.push(item);
            }
        });
    }
    
    // ������������ - �����ͷ��鴴����ͬ���
    let returnMoneyItems = [];
    let depositMoneyItems = [];
    
    // �� money_transactions ����ȡ����
    if (Array.isArray(transactionData.money_transactions) && transactionData.money_transactions.length > 0) {
        transactionData.money_transactions.forEach(item => {
            console.log('����������:', item);
            if (item.return_amount > 0) {
                // �����������أ���ӵ������б�����ǻ����б�
                if (item.return_purpose === '����' && item.return_material_type) {
                    console.log(`�����������ݣ���������: ${item.return_material_type}`);
                    buyMaterialItems.push({
                        material_type: item.return_material_type,
                        buy_weight: item.return_weight || 0,
                        material_price: item.return_amount / (item.return_weight || 1),
                        buy_total: item.return_amount || 0,
                        note: item.note || ''
                    });
                } else {
                    // ֻ�з����ϵĻ���ż��뻹���б�
                    returnMoneyItems.push(item);
                }
            } else if (item.store_amount > 0) {
                depositMoneyItems.push(item);
            }
        });
    }
    
    console.log('������Ŀ����:', returnMaterialItems.length);
    console.log('������Ŀ����:', buyMaterialItems.length);
    console.log('������Ŀ����:', storeMaterialItems.length);
    console.log('������Ŀ����:', depositMaterialItems.length); // ���������Ŀ����
    console.log('������Ŀ����:', returnMoneyItems.length);
    console.log('�����Ŀ����:', depositMoneyItems.length);
    
    // �������ϱ����������ݣ�
    if (returnMaterialItems.length > 0) {
        console.log('��ʼ�������ϱ��');
        // ��ӻ��ϱ��
        addRowByType('return_material');
        
        // ��ȡ�´����ı����
        const returnMaterialTable = document.querySelector('.transaction-row[data-type="return_material"] tbody');
        if (returnMaterialTable) {
            // ���Ĭ�ϴ�������
            returnMaterialTable.innerHTML = '';
            
            // ���������
            returnMaterialItems.forEach(item => {
                const rowHTML = createReturnMaterialRow();
                returnMaterialTable.insertAdjacentHTML('beforeend', rowHTML);
            
                // ��ȡ���в��������
                const newRow = returnMaterialTable.lastElementChild;
                if (newRow) {
                    // ����ֵ
                    const returnWeight = newRow.querySelector('.return-weight');
                    const returnMaterialType = newRow.querySelector('[name="return_material_type[]"]');
                    const returnSource = newRow.querySelector('[name="return_material_source[]"]');
                    const note = newRow.querySelector('[name="material_note[]"]');
        
        if (returnWeight) returnWeight.value = item.return_weight || '';
        if (note) note.value = item.note || '';
        
                    // ��������ѡ���
        if (returnMaterialType && item.return_material_type) {
            console.log(`���û��ϵľ�������: ${item.return_material_type}`);
            returnMaterialType.setAttribute('data-original-value', item.return_material_type);
            // ��������Ƿ��Ѽ��أ�����Ѽ�������������
            if (window.oldMaterials && window.oldMaterials.length > 0) {
                updateOldMaterialSelect(returnMaterialType, item.return_material_type);
                returnMaterialType.value = item.return_material_type;
                console.log(`���Ͼ��������������: ${returnMaterialType.value}`);
            } else {
                updateOldMaterialSelect(returnMaterialType, item.return_material_type);
                returnMaterialType.value = item.return_material_type;
            }
        }
        
        if (returnSource && item.return_source) {
                        console.log('���û�����Դ:', item.return_source);
                        // ȷ������ѡ���Ѽ���
            initializeReturnMaterialSource(returnSource);
                        // ����ѡ��ֵ
            returnSource.value = item.return_source;
        }
                    
                    // ��ʼ�����е��¼�����
                    initializeRowElements(newRow);
                }
            });
        } else {
            console.error('δ�ҵ����ϱ��Ԫ��');
        }
    }
    
    // �������ϱ����������ݣ�
    if (buyMaterialItems.length > 0) {
        console.log('��ʼ�������ϱ��');
        // ������ϱ��
        addRowByType('buy_material');
        
        // ��ȡ�´����ı����
        const buyMaterialTable = document.querySelector('.transaction-row[data-type="buy_material"] tbody');
        if (buyMaterialTable) {
            // ���Ĭ�ϴ�������
            buyMaterialTable.innerHTML = '';
            
            // ���������
            buyMaterialItems.forEach(item => {
                const rowHTML = createBuyMaterialRow();
                buyMaterialTable.insertAdjacentHTML('beforeend', rowHTML);
                
                // ��ȡ���в��������
                const newRow = buyMaterialTable.lastElementChild;
                if (newRow) {
                    // ����ֵ
                    const materialType = newRow.querySelector('[name="buy_material_type[]"]');
                    const buyWeight = newRow.querySelector('[name="buy_weight[]"]');
                    const materialPrice = newRow.querySelector('[name="material_price[]"]');
                    const buyTotal = newRow.querySelector('[name="buy_total[]"]');
                    const note = newRow.querySelector('[name="buy_note[]"]');

        if (buyWeight) buyWeight.value = item.buy_weight || '';
        if (materialPrice) materialPrice.value = item.material_price || '';
        if (buyTotal) buyTotal.value = item.buy_total || item.return_amount || '';
        if (note) note.value = item.note || '';
        
                    // ��������ѡ���
        if (materialType && item.material_type) {
            console.log(`�������ϵľ�������: ${item.material_type}`);
            materialType.setAttribute('data-original-value', item.material_type);
            // ��������Ƿ��Ѽ��أ�����Ѽ�������������
            if (window.oldMaterials && window.oldMaterials.length > 0) {
                updateOldMaterialSelect(materialType, item.material_type);
                materialType.value = item.material_type;
                console.log(`���Ͼ��������������: ${materialType.value}`);
            } else {
                updateOldMaterialSelect(materialType, item.material_type);
                materialType.value = item.material_type;
            }
        }
                    
                    // ��ʼ�����е��¼�����
                    initializeRowElements(newRow);
            }
        });
    } else {
            console.error('δ�ҵ����ϱ��Ԫ��');
        }
    }
    
    // �������ϱ�񣨽�����ʵ�ʼ�������ʱ��
    if (depositMaterialItems.length > 0) {
        // ����Ƿ�����ʵ���ݣ����������ǿ��ֶ�
        const hasRealDepositData = depositMaterialItems.some(item => 
            parseFloat(item.deposit_weight || 0) > 0 || 
            parseFloat(item.actual_deposit_weight || 0) > 0
        );
        
        if (hasRealDepositData) {
            console.log('��ʼ�������ϱ����ʵ�����ݣ�');
        // ��Ӽ��ϱ��
        addRowByType('deposit_material');
        
        // ��ȡ�´����ı����
        const depositMaterialTable = document.querySelector('.transaction-row[data-type="deposit_material"] tbody');
        if (depositMaterialTable) {
            // ���Ĭ�ϴ�������
            depositMaterialTable.innerHTML = '';
            
            // ���������
            depositMaterialItems.forEach(item => {
                const rowHTML = createDepositMaterialRow();
                depositMaterialTable.insertAdjacentHTML('beforeend', rowHTML);
                
                // ��ȡ���в��������
                const newRow = depositMaterialTable.lastElementChild;
                if (newRow) {
                    // ����ֵ
                    const depositMaterialType = newRow.querySelector('[name="deposit_material_type[]"]');
                    const depositWeight = newRow.querySelector('[name="deposit_weight[]"]');
                    const actualDepositWeight = newRow.querySelector('[name="actual_deposit_weight[]"]');
                    const depositLoss = newRow.querySelector('[name="deposit_loss[]"]');
                    const note = newRow.querySelector('[name="material_note[]"]');
        
                    // ȷ��ת��Ϊ��ֵ
        const depositWeightValue = parseFloat(item.deposit_weight || 0);
        const actualDepositWeightValue = parseFloat(item.actual_deposit_weight || 0);
        const depositLossValue = parseFloat(item.deposit_loss || 0);
        
                    // ��ʽ����Ϊ�ַ�����ʽ����ֵ
        if (depositWeight) depositWeight.value = depositWeightValue.toString();
        if (actualDepositWeight) actualDepositWeight.value = actualDepositWeightValue.toString();
        if (depositLoss) depositLoss.value = depositLossValue.toString();
        if (note) note.value = item.note || '';
        
                    // ��������ѡ���
        if (depositMaterialType && item.deposit_material_type) {
            console.log(`���ü��ϵľ�������: ${item.deposit_material_type}`);
            depositMaterialType.setAttribute('data-original-value', item.deposit_material_type);
            // ��������Ƿ��Ѽ��أ�����Ѽ�������������
            if (window.oldMaterials && window.oldMaterials.length > 0) {
                updateOldMaterialSelect(depositMaterialType, item.deposit_material_type);
                depositMaterialType.value = item.deposit_material_type;
                console.log(`���Ͼ��������������: ${depositMaterialType.value}`);
            } else {
                updateOldMaterialSelect(depositMaterialType, item.deposit_material_type);
                depositMaterialType.value = item.deposit_material_type;
            }
        }
                    
                    // ��ʼ�����е��¼�����
                    initializeRowElements(newRow);
                }
            });
        } else {
            console.error('δ�ҵ����ϱ��Ԫ��');
        }
        } else {
            console.log('�������ݲ�����ʵ����ֵ�����������ϱ��');
        }
    } else {
        console.log('û�м������ݣ����������ϱ��');
    }
    
    // �������ϱ����������ݣ�
    if (storeMaterialItems.length > 0) {
        console.log('��ʼ�������ϱ�񣬹��� ' + storeMaterialItems.length + ' ������');
        
        // ȷ�����Ϲ�Ӧ�������Ѽ���
        if (!window.goldSuppliers || window.goldSuppliers.length === 0) {
            console.log('���ϱ����Ҫ���Ϲ�Ӧ�����ݣ���������δ����');
            // ʹ��Ĭ�Ϲ�Ӧ������
            useDefaultSuppliers();
        }
        
        // ��Ӵ��ϱ��
        addRowByType('store_material');
        
        // ��ȡ�´����ı����
        const storeMaterialTable = document.querySelector('.transaction-row[data-type="store_material"] tbody');
        if (storeMaterialTable) {
            // ���Ĭ�ϴ�������
            storeMaterialTable.innerHTML = '';
            
            // ���������
            storeMaterialItems.forEach((item, index) => {
                const rowHTML = createStoreMaterialRow();
                storeMaterialTable.insertAdjacentHTML('beforeend', rowHTML);
                
                // ��ȡ���в��������
                const newRow = storeMaterialTable.lastElementChild;
                if (newRow) {
                    // ��ȡ���Ԫ��
                    const storeMaterialType = newRow.querySelector('[name="store_material_type[]"]');
                    const storeSource = newRow.querySelector('[name="store_material_source[]"]');
                    const storeWeight = newRow.querySelector('[name="store_weight[]"]');
                    const note = newRow.querySelector('[name="material_note[]"]');
        
                    console.log(`��������� #${index + 1}:`, item);
        
                    // �ȳ�ʼ������ѡ���
        if (storeMaterialType && window.oldMaterials) {
            const materialType = item.store_material_type || item.material_type || '';
            console.log(`���ô��ϵľ�������: ${materialType}`);
            storeMaterialType.setAttribute('data-original-value', materialType);
            // ��������Ƿ��Ѽ��أ�����Ѽ�������������
            if (window.oldMaterials && window.oldMaterials.length > 0) {
                updateOldMaterialSelect(storeMaterialType, materialType);
                storeMaterialType.value = materialType;
                console.log(`���Ͼ��������������: ${storeMaterialType.value}`);
                // ��Ҫ��������������ͺ����¼��㱾������
                updateCalculations();
                console.log(`���Ͼ����������ú����¼������`);
            } else {
                updateOldMaterialSelect(storeMaterialType, materialType);
                storeMaterialType.value = materialType;
                updateCalculations();
            }
        }
        
                    // ר�Ŵ��������Դ
        if (storeSource) {
                        console.log(`��ʼ��������Դѡ��򣬵�ǰ����Դ:`, item.store_source || item.material_source || '');
                        
                        // ȷ������ѡ���Ѽ���
            initializeStoreMaterialSource(storeSource);
            
                        // ����ѡ��ֵ
                        if (item.store_source || item.material_source) {
                            const sourceValue = item.store_source || item.material_source;
                            
                // ���ѡ���Ƿ����
                let sourceExists = false;
                for (let i = 0; i < storeSource.options.length; i++) {
                    if (storeSource.options[i].value === sourceValue) {
                        storeSource.selectedIndex = i;
                        sourceExists = true;
                                    console.log(`�ҵ���ѡ�д�����Դ: ${sourceValue}`);
                        break;
                    }
                }
                
                // ���ѡ����ڣ������
                if (!sourceExists && sourceValue !== '') {
                    const option = document.createElement('option');
                    option.value = sourceValue;
                    option.textContent = sourceValue;
                    storeSource.appendChild(option);
                    storeSource.value = sourceValue;
                                console.log(`��Ӳ�ѡ�д�����Դ: ${sourceValue}`);
            }
        }
    }
    
                    // ��������ֵ
                    if (storeWeight) storeWeight.value = item.store_weight || '';
                    if (note) note.value = item.note || '';
                }
            });
            
            // ������������������
            const storeSourceSelects = storeMaterialTable.querySelectorAll('[name="store_material_source[]"]');
            console.log(`���� ${storeSourceSelects.length} ��������Դѡ���`);
            storeSourceSelects.forEach(select => initializeStoreMaterialSource(select));
        } else {
            console.error('δ�ҵ����ϱ��Ԫ��');
        }
    }
    
    // ������������������ݣ�
    if (returnMoneyItems.length > 0) {
        console.log('��ʼ����������');
        // ��ӻ�����
        addRowByType('return_money');
        
        // ��ȡ�´����ı����
        const returnMoneyTable = document.querySelector('.transaction-row[data-type="return_money"] tbody');
        if (returnMoneyTable) {
            // ���Ĭ�ϴ�������
            returnMoneyTable.innerHTML = '';
            
            // ���������
            returnMoneyItems.forEach(item => {
                const rowHTML = createReturnMoneyRow();
                returnMoneyTable.insertAdjacentHTML('beforeend', rowHTML);
                
                // ��ȡ���в��������
                const newRow = returnMoneyTable.lastElementChild;
                if (newRow) {
                    // ����ֵ
                    const returnAmount = newRow.querySelector('[name="return_amount[]"]');
                    const returnSource = newRow.querySelector('[name="return_money_source[]"]');
                    const returnPurpose = newRow.querySelector('[name="return_purpose[]"]');
                    const note = newRow.querySelector('[name="money_note[]"]');
        
                    // ��ʼ��������
                    if (returnSource) {
                        // ȷ������ѡ���Ѽ���
                        initializeReturnMoneySource(returnSource);
                    }
                    
                    if (returnPurpose) {
                        // ȷ������ѡ���Ѽ���
                        initializeReturnMoneyPurpose(returnPurpose);
                    }
                    
                    // ����ֵ
        if (returnAmount) returnAmount.value = item.return_amount || '';
        if (note) note.value = item.note || '';
        
                    // ��������ѡ���
        if (returnSource && item.return_source) {
            console.log('���û�����Դ:', item.return_source);
            // ����Ƿ��ж�Ӧѡ��
            let hasOption = false;
            for (let i = 0; i < returnSource.options.length; i++) {
                if (returnSource.options[i].value === item.return_source) {
                    hasOption = true;
                    break;
                }
            }

            // ���û���ҵ�ѡ����һ��
            if (!hasOption) {
                const option = document.createElement('option');
                option.value = item.return_source;
                option.textContent = item.return_source;
                returnSource.appendChild(option);
                console.log(`Ϊ������Դ�����δֵ֪: ${item.return_source}`);
            }

            // ����ѡ��ֵ
            returnSource.value = item.return_source;
        }
        
        if (returnPurpose && item.return_purpose) {
            // ����Ƿ��ж�Ӧѡ��
            let hasOption = false;
            for (let i = 0; i < returnPurpose.options.length; i++) {
                if (returnPurpose.options[i].value === item.return_purpose) {
                    hasOption = true;
                    break;
                }
            }

            // ���û���ҵ�ѡ����һ��
            if (!hasOption) {
                const option = document.createElement('option');
                option.value = item.return_purpose;
                option.textContent = item.return_purpose;
                returnPurpose.appendChild(option);
                console.log(`Ϊ������;�����δֵ֪: ${item.return_purpose}`);
            }

            // ����ѡ��ֵ
            returnPurpose.value = item.return_purpose;
        }
    }
            });
        } else {
            console.error('δ�ҵ�������Ԫ��');
        }
    }
    
    // �����������������ݣ�
    if (depositMoneyItems.length > 0) {
        console.log('��ʼ���������');
        // ��Ӵ����
        addRowByType('deposit_money');
        
        // ��ȡ�´����ı����
        const depositMoneyTable = document.querySelector('.transaction-row[data-type="deposit_money"] tbody');
        if (depositMoneyTable) {
            // ���Ĭ�ϴ�������
            depositMoneyTable.innerHTML = '';
            
            // ���������
            depositMoneyItems.forEach(item => {
                const rowHTML = createDepositMoneyRow();
                depositMoneyTable.insertAdjacentHTML('beforeend', rowHTML);
                
                // ��ȡ���в��������
                const newRow = depositMoneyTable.lastElementChild;
                if (newRow) {
                    // ����ֵ
                    const storeAmount = newRow.querySelector('[name="deposit_amount[]"]');
                    const depositType = newRow.querySelector('[name="deposit_source[]"]');
                    const note = newRow.querySelector('[name="money_note[]"]');
        
                    // ��ʼ��������
                    if (depositType) {
                        // ȷ������ѡ���Ѽ���
                        initializeDepositMoneySource(depositType);
                    }
                    
                    // ����ֵ
        if (storeAmount) storeAmount.value = item.store_amount || '';
        if (note) note.value = item.note || '';
        
                    // ��������ѡ���
        if (depositType && item.store_source) {
            console.log('���ô����Դ:', item.store_source);
                            depositType.value = item.store_source;
                        }
            }
        });
    } else {
            console.error('δ�ҵ������Ԫ��');
                    }
                }
                
    // ִֻ��һ�μ����һ��ѡ������
    console.log('�༭���ݼ�����ɣ�ִ�����ո���...');

    // ���ؼ���״̬����ʾ��������
    const loadingPlaceholder = document.getElementById('loading-placeholder');
    if (loadingPlaceholder) {
        loadingPlaceholder.style.display = 'none';
    }

    // ��ֹ�м�ˢ����˸����ӳ�ʼ�����
    window.isLoadingEditData = true;

    // ���������Ѿ��ڼ��ع�������ȷ���ã��ӳ�ִ�����ռ���
    setTimeout(() => {
        window.isLoadingEditData = false;
        // ֻ�����н�������ʱ����Ҫ����
        if (document.querySelectorAll('.transaction-row tbody tr').length > 0) {
            updateCalculations();
        }
    }, 100);

    if (!window.allSelectsUpdated) {
        window.allSelectsUpdated = true;
        console.log('ִ��ѡ������ո���');
        updateAllSelects();
    }

    } catch (error) {
        handleError(error, '���ر༭����');

        // ���Ի����Ĵ���ָ�
        try {
            // ����ȷ��������UIԪ�ؿ���
            updateCalculations();
        } catch (recoveryError) {
            console.error('����ָ�ʧ��:', recoveryError);
        }
    }
}

// ��������ѡ������Ż�
function updateAllSelects() {
    console.log('������������ѡ���...');
    
    // ��ֹ�ظ�����
    if (window.allSelectsUpdated) {
        console.log('����ѡ����Ѿ����¹��������ظ�����');
        return;
    }
    window.allSelectsUpdated = true;
    
    // ���¾���ѡ���
    if (window.oldMaterials && window.oldMaterials.length > 0) {
        const oldMaterialSelects = document.querySelectorAll('.old-material-select');
        oldMaterialSelects.forEach(select => {
            // ���浱ǰֵ
            const currentValue = select.value;
            
            // ���ѡ����һ��
            while (select.options.length > 1) {
                select.remove(1);
            }
            
            // ��Ӿ���ѡ��
            window.oldMaterials.forEach(material => {
                const option = document.createElement('option');
                option.value = material.name;
                option.textContent = material.name;
                select.appendChild(option);
            });
            
            // �ָ�ѡ��ֵ
            if (currentValue) {
                select.value = currentValue;
            }
        });
        // console.log(`�Ѹ��� ${oldMaterialSelects.length} ������ѡ���`); // ����������ע�͵�
    }

    // ���»�����Դѡ��򣨽�����Ԫ��ʱ��
    const returnMaterialSources = document.querySelectorAll('.return-material-source');
    if (returnMaterialSources.length > 0) {
        returnMaterialSources.forEach(select => {
            initializeReturnMaterialSource(select);
        });
        // console.log(`�Ѹ��� ${returnMaterialSources.length} ��������Դѡ���`); // ����������ע�͵�
    }

    // ���´�����Դѡ��򣨽�����Ԫ��ʱ��
    const storeMaterialSources = document.querySelectorAll('.store-material-source');
    if (storeMaterialSources.length > 0) {
        storeMaterialSources.forEach(select => {
            initializeStoreMaterialSource(select);
        });
        // console.log(`�Ѹ��� ${storeMaterialSources.length} ��������Դѡ���`); // ����������ע�͵�
    }

    // ���»�����Դѡ��򣨽�����Ԫ��ʱ��
    const returnMoneySources = document.querySelectorAll('.return-money-source');
    if (returnMoneySources.length > 0) {
        returnMoneySources.forEach(select => {
            initializeReturnMoneySource(select);
        });
        // console.log(`�Ѹ��� ${returnMoneySources.length} ��������Դѡ���`); // ����������ע�͵�
    }

    // ���´����Դѡ��򣨽�����Ԫ��ʱ��
    const depositSources = document.querySelectorAll('[name="deposit_source[]"]');
    if (depositSources.length > 0) {
        depositSources.forEach(select => {
            initializeDepositMoneySource(select);
        });
        // console.log(`�Ѹ��� ${depositSources.length} �������Դѡ���`); // ����������ע�͵�
    }

    // ���»�����;ѡ��򣨽�����Ԫ��ʱ��
    const returnPurposes = document.querySelectorAll('[name="return_purpose[]"]');
    if (returnPurposes.length > 0) {
        returnPurposes.forEach(select => {
            initializeReturnMoneyPurpose(select);
        });
        // console.log(`�Ѹ��� ${returnPurposes.length} ��������;ѡ���`); // ����������ע�͵�
    }
}

// ���������
function addMaterialRow() {
    const materialTable = document.getElementById('material-table').querySelector('tbody');
    if (!materialTable) return;
    
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>
            <input type="number" class="form-control form-control-sm return-weight" name="return_weight[]" step="0.01" value="" oninput="handleReturnInputChange(this)">
        </td>
        <td>
            <select class="form-control form-control-sm old-material-select" name="return_material_type[]" onchange="updateCalculations()">
                <option value="">��ѡ��</option>
            </select>
        </td>
        <td>
            <select class="form-control form-control-sm return-material-source" name="return_material_source[]" onchange="handleReturnSourceChange(this)">
                <option value="">��ѡ��</option>
                <option value="�ϲ����">�ϲ����</option>
                <option value="���ϵֿ�">���ϵֿ�</option>
            </select>
        </td>
        <td>
            <!-- actual_return_weight�ֶ����Ƴ� -->
        </td>
        <td>
            <input type="number" class="form-control form-control-sm wastage-loss" name="wastage_loss[]" step="0.01" value="" readonly>
        </td>
        <td>
            <input type="number" class="form-control form-control-sm store-weight" name="store_weight[]" step="0.01" value="" oninput="updateCalculations()">
        </td>
        <td>
            <select class="form-control form-control-sm old-material-select" name="store_material_type[]" onchange="updateCalculations()">
                <option value="">��ѡ��</option>
            </select>
        </td>
        <td>
            <select class="form-control form-control-sm store-material-source" name="store_material_source[]" onchange="updateCalculations()">
                <option value="">��ѡ��</option>
                <option value="�ϲ����">�ϲ����</option>
                <option value="���ϵֿ�">���ϵֿ�</option>
            </select>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeMaterialRow(this)">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    `;
    materialTable.appendChild(newRow);
    
    // ���¾���ѡ���
    const materialSelects = newRow.querySelectorAll('.old-material-select');
    updateOldMaterialSelects(materialSelects);
    
    // ���¾�����Դѡ���
    const sourceSelects = newRow.querySelectorAll('.return-material-source, .store-material-source');
    sourceSelects.forEach(select => {
        if (select.classList.contains('return-material-source')) {
            initializeReturnMaterialSource(select);
        } else if (select.classList.contains('store-material-source')) {
            initializeStoreMaterialSource(select);
        }
    });
    
    return newRow;
}

// ��ӿ�����
function addMoneyRow() {
    const moneyTable = document.getElementById('money-table').querySelector('tbody');
    if (!moneyTable) return;
    
    const newRow = document.createElement('tr');
    newRow.innerHTML = `
        <td>
            <input type="number" class="form-control form-control-sm return-amount" name="return_amount[]" step="1" value="" oninput="updateCalculations()">
        </td>
        <td>
            <select class="form-control form-control-sm return-money-source" name="return_money_source[]" onchange="updateCalculations()">
                <option value="">��ѡ��</option>
            </select>
        </td>
        <td>
            <select class="form-control form-control-sm" name="return_purpose[]" onchange="updateCalculations()">
                <option value="������">������</option>
                <option value="����">����</option>
            </select>
        </td>
        <td></td>
        <td></td>
        <td>
            <input type="number" class="form-control form-control-sm store-amount" name="store_amount[]" step="1" value="" oninput="updateCalculations()">
        </td>
        <td>
            <select class="form-control form-control-sm" name="store_money_source[]" onchange="updateCalculations()">
                <option value="">��ѡ��</option>
                <option value="�ֽ�">�ֽ�</option>
                <option value="����ת��">����ת��</option>
                <option value="֧����">֧����</option>
                <option value="΢��">΢��</option>
            </select>
        </td>
        <td>
            <button type="button" class="btn btn-sm btn-danger" onclick="removeMoneyRow(this)">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    `;
    moneyTable.appendChild(newRow);
    
    // ���¾���ѡ���
    const materialSelects = newRow.querySelectorAll('.old-material-select');
    updateOldMaterialSelects(materialSelects);
    
    // �����ʽ���Դѡ���
    const moneySourceSelect = newRow.querySelector('.return-money-source');
    if (moneySourceSelect) {
        // ��Ӵ��ֿ�ѡ��
        const offsetOption = document.createElement('option');
        offsetOption.value = '���ֿ�';
        offsetOption.textContent = '���ֿ�';
        moneySourceSelect.appendChild(offsetOption);
        
        // ��ӳ����˻�ѡ��
        ['�ֽ�', '����ת��', '֧����', '΢��'].forEach(account => {
            const option = document.createElement('option');
            option.value = account;
            option.textContent = account;
            moneySourceSelect.appendChild(option);
        });
    }
    
    // ���´����Դѡ���
    const storeMoneySourceSelect = newRow.querySelector('[name="store_money_source[]"]');
    if (storeMoneySourceSelect) {
        // ��ӳ����˻�ѡ��
        ['�ֽ�', '����ת��', '֧����', '΢��'].forEach(account => {
            const option = document.createElement('option');
            option.value = account;
            option.textContent = account;
            storeMoneySourceSelect.appendChild(option);
        });
    }
    
    return newRow;
}

// �Ƴ�������
function removeMaterialRow(button) {
    const row = button.closest('tr');
    if (row) {
        row.remove();
    updateCalculations();
    }
}

// �Ƴ�������
function removeMoneyRow(button) {
    const row = button.closest('tr');
    if (row) {
        row.remove();
        updateCalculations();
    }
}

// ɾ���л������ֶε��߼�

// ��ӱ༭ģʽ���ݼ��غ���

// ���updateDepositLoss����������������
function updateDepositLoss(input) {
    const row = input.closest('tr');
    if (!row) {
        console.error('updateDepositLoss: �Ҳ�������Ԫ��');
        return;
    }
    
    // ��ȡ�����ֶβ�������ʽת��
    const depositWeightInput = row.querySelector('[name="deposit_weight[]"]');
    const actualDepositWeightInput = row.querySelector('[name="actual_deposit_weight[]"]');
    const depositLossInput = row.querySelector('[name="deposit_loss[]"]');
    
    if (!depositWeightInput || !actualDepositWeightInput || !depositLossInput) {
        console.error('updateDepositLoss: �Ҳ�����Ҫ�������ֶ�', {
            depositWeightInput: !!depositWeightInput,
            actualDepositWeightInput: !!actualDepositWeightInput,
            depositLossInput: !!depositLossInput
        });
        return;
    }
    
    // ȷ������ֵΪ����
    const depositWeight = parseFloat(depositWeightInput.value) || 0;
    const actualDepositWeight = parseFloat(actualDepositWeightInput.value) || 0;
    
    console.log('����������:', {
        depositWeight: depositWeight,
        actualDepositWeight: actualDepositWeight
    });
    
    // ������� = ���Ͽ��� - ʵ�ʵ��Ͽ���
    const depositLoss = depositWeight - actualDepositWeight;
    
    // �������ֵ��������λС��
    depositLossInput.value = depositLoss.toFixed(2);
    
    console.log('������ - ���:', depositLoss.toFixed(2));
    
    // �����ܼ���
    updateCalculations();
}

// ��� addRowByType �����Ƿ���ڣ���������ڣ������

// ɾ���ظ��ĺ������壬ʹ������ͳһ�İ汾

// ��ʼ������ʽ���Դ����ѡ��
function initializeDepositMoneySource(selectElement) {
    if (!selectElement) return;
    
    // ���浱ǰֵ������У�
    const currentValue = selectElement.value;
    
    // �������ѡ������һ����ѡ��
    while (selectElement.options.length > 1) {
        selectElement.remove(1);
    }
    
    // ֻ����˻���Ϣ���е��˻�ѡ��
    if (window.accounts && window.accounts.length > 0) {
        window.accounts.forEach(account => {
                const option = document.createElement('option');
                option.value = account.name;
                option.textContent = account.name;
                selectElement.appendChild(option);
        });
        console.log(`����� ${window.accounts.length} ���˻�ѡ������Դ`);
    } else {
        console.warn('û���ҵ��˻����ݣ������Դ������Ϊ��');
    }
    
    // �����֮ǰ��ֵ�����Իָ�
    if (currentValue && Array.from(selectElement.options).some(opt => opt.value === currentValue)) {
        selectElement.value = currentValue;
    }
}

// ��ʼ��������Դѡ��������ϵֿۺͽ��Ϲ�Ӧ�̣�
function initializeReturnMaterialSource(selectElement) {
    if (!selectElement) return;
    
    // ���浱ǰֵ������У�
    const currentValue = selectElement.value;
    
    // �������ѡ������һ����ѡ��
    while (selectElement.options.length > 1) {
        selectElement.remove(1);
    }
    
    // ��Ӵ��ϵֿ�ѡ���������ڣ�
    let hasOffsetOption = false;
    for (let i = 0; i < selectElement.options.length; i++) {
        if (selectElement.options[i].value === '���ϵֿ�') {
            hasOffsetOption = true;
            break;
        }
    }
    
    if (!hasOffsetOption) {
        const offsetOption = document.createElement('option');
        offsetOption.value = '���ϵֿ�';
        offsetOption.textContent = '���ϵֿ�';
        selectElement.appendChild(offsetOption);
    }
    
    // ��ӽ��Ϲ�Ӧ��ѡ��
    if (window.goldSuppliers && window.goldSuppliers.length > 0) {
        window.goldSuppliers.forEach(supplier => {
            const option = document.createElement('option');
            // ʹ�ù�Ӧ��������Ϊֵ����ʾ�ı�
            option.value = supplier.name;
            option.textContent = supplier.name;
            selectElement.appendChild(option);
        });
        console.log(`����� ${window.goldSuppliers.length} �����Ϲ�Ӧ��ѡ�������Դ`);
    } else {
        console.warn('û���ҵ����Ϲ�Ӧ������');
    }
    
    // �����֮ǰ��ֵ�����Իָ�
    if (currentValue && Array.from(selectElement.options).some(opt => opt.value === currentValue)) {
        selectElement.value = currentValue;
    }
}

// ��ӳ�ʼ��������;������ĺ���
function initializeReturnMoneyPurpose(selectElement) {
    if (!selectElement) return;
    
    // ���浱ǰֵ������У�
    const currentValue = selectElement.value;
    
    // �������ѡ��
    while (selectElement.options.length > 0) {
        selectElement.remove(0);
    }
    
    // ��ӱ�׼ѡ��
    const options = [
        { value: '', text: '��ѡ�񻹿���;' },
        { value: '������', text: '������' },
        { value: '����', text: '����' }
    ];
    
    options.forEach(opt => {
        const option = document.createElement('option');
        option.value = opt.value;
        option.textContent = opt.text;
        selectElement.appendChild(option);
    });
    
    // �����֮ǰ��ֵ�����Իָ�
    if (currentValue && Array.from(selectElement.options).some(opt => opt.value === currentValue)) {
        selectElement.value = currentValue;
    }
}

// ��ӳ�ʼ��������Դ������ĺ���
function initializeReturnMoneySource(selectElement) {
    if (!selectElement) return;
    
    // ���浱ǰֵ������У�
    const currentValue = selectElement.value;
    
    // �������ѡ������һ����ѡ��
    while (selectElement.options.length > 1) {
        selectElement.remove(1);
    }
    
    // ��Ӵ��ֿ۹̶�ѡ��
    const offsetOption = document.createElement('option');
    offsetOption.value = '���ֿ�';
    offsetOption.textContent = '���ֿ�';
    selectElement.appendChild(offsetOption);
    
    // ֻ����˻���Ϣ���е��˻�ѡ��
    if (window.accounts && window.accounts.length > 0) {
        window.accounts.forEach(account => {
            const option = document.createElement('option');
            option.value = account.name;
            option.textContent = account.name;
            selectElement.appendChild(option);
        });
        console.log(`����� ${window.accounts.length} ���˻�ѡ�������Դ`);
    } else {
        console.warn('û���ҵ��˻����ݣ�������Դ������ֻ�д��ֿ�ѡ��');
    }
    
    // �����֮ǰ��ֵ�����Իָ�
    if (currentValue && Array.from(selectElement.options).some(opt => opt.value === currentValue)) {
        selectElement.value = currentValue;
    }
}

// ɾ���ظ���fetchGoldSuppliers������ʹ��������Ż��汾

// ���½��Ϲ�Ӧ��ѡ���
function updateGoldSupplierSelects() {
    // console.log('��ʼ���»�����Դѡ���...'); // ����������ע�͵�
    
    const returnSourceSelects = document.querySelectorAll('.return-material-source');
    let updatedCount = 0;
    
    returnSourceSelects.forEach(select => {
        // ���浱ǰֵ
        const currentValue = select.value;
        
        // ��ʼ��ѡ���
        initializeReturnMaterialSource(select);
        
        // �����֮ǰ��ֵ�����Իָ�
        if (currentValue && Array.from(select.options).some(opt => opt.value === currentValue)) {
            select.value = currentValue;
            updatedCount++;
        }
    });
    
    console.log(`������ ${returnSourceSelects.length} ��������Դѡ��򣬻ָ��� ${updatedCount} ��֮ǰ��ֵ`);
}

// ��ʼ��������Դѡ��
function initializeStoreMaterialSource(selectElement) {
    if (!selectElement) {
        console.warn('��ʼ��������Դʧ�ܣ�ѡ���Ԫ��Ϊ��');
        return;
    }
    
    console.log('��ʼ��ʼ��������Դѡ���');
    
    // ���浱ǰֵ������У�
    const currentValue = selectElement.value;
    console.log('��ǰ������Դֵ:', currentValue);
    
    // �������ѡ������һ����ѡ��
    while (selectElement.options.length > 1) {
        selectElement.remove(1);
    }
    
    // ��ӽ��Ϲ�Ӧ��ѡ��
    if (window.goldSuppliers && window.goldSuppliers.length > 0) {
        console.log(`�� window.goldSuppliers ��� ${window.goldSuppliers.length} ����Ӧ��ѡ��`);
        window.goldSuppliers.forEach(supplier => {
            const option = document.createElement('option');
            // ʹ�ù�Ӧ��������Ϊֵ����ʾ�ı�
            option.value = supplier.name;
            option.textContent = supplier.name;
            selectElement.appendChild(option);
        });
    } else {
        console.warn('û���ҵ����Ϲ�Ӧ�����ݣ�����API�ӿ�');
    }
    
    // �����֮ǰ��ֵ�����Իָ�
    if (currentValue) {
        let optionExists = false;
        // ���ѡ���Ƿ����
        for (let i = 0; i < selectElement.options.length; i++) {
            if (selectElement.options[i].value === currentValue) {
                selectElement.selectedIndex = i;
                optionExists = true;
                console.log(`�ָ�������Դѡ����ֵ: ${currentValue}`);
                break;
            }
        }
        
        // ���ѡ����ڣ������
        if (!optionExists && currentValue !== '') {
            const option = document.createElement('option');
            option.value = currentValue;
            option.textContent = currentValue;
            selectElement.appendChild(option);
            selectElement.value = currentValue;
            console.log(`��Ӳ�ѡ��֮ǰ��ֵ: ${currentValue}`);
        }
    }
    
    console.log(`��ɳ�ʼ��������Դѡ������� ${selectElement.options.length} ��ѡ��`);
}

// ������������ݵ�����̨�ĺ���
function logTableData() {
    // console.log('===== ������� =====');
    const allData = {
        return_material: [],
        buy_material: [],
        store_material: [],
        return_money: [],
        deposit_money: []
    };
    
    // ��ȡ������
    const allRows = document.querySelectorAll('.transaction-row tbody tr');
    
    allRows.forEach(tr => {
        const container = tr.closest('.transaction-row');
        if (!container) return;
        
        const type = container.getAttribute('data-type');
        
        // ���������
        if (type === 'return_material') {
            const rowData = {
                material_type: tr.querySelector('[name="return_material_type[]"]')?.value || '',
                material_source: tr.querySelector('[name="return_material_source[]"]')?.value || '',
                return_weight: parseFloat(tr.querySelector('[name="return_weight[]"]')?.value) || 0,
                note: tr.querySelector('[name="material_note[]"]')?.value || ''
            };
            allData.return_material.push(rowData);
        }
        // ����������
        else if (type === 'buy_material') {
            const rowData = {
                material_type: tr.querySelector('[name="buy_material_type[]"]')?.value || '',
                buy_weight: parseFloat(tr.querySelector('[name="buy_weight[]"]')?.value) || 0,
                material_price: parseFloat(tr.querySelector('[name="material_price[]"]')?.value) || 0,
                buy_amount: parseFloat(tr.querySelector('[name="buy_amount[]"]')?.value) || 0,
                note: tr.querySelector('[name="money_note[]"]')?.value || ''
            };
            allData.buy_material.push(rowData);
        }
        // ���������
        else if (type === 'store_material') {
            const rowData = {
                material_type: tr.querySelector('[name="store_material_type[]"]')?.value || '',
                material_source: tr.querySelector('[name="store_material_source[]"]')?.value || '',
                store_weight: parseFloat(tr.querySelector('[name="store_weight[]"]')?.value) || 0,
                note: tr.querySelector('[name="material_note[]"]')?.value || ''
            };
            allData.store_material.push(rowData);
        }
        // ���������
        else if (type === 'return_money') {
            const rowData = {
                return_amount: parseFloat(tr.querySelector('[name="return_amount[]"]')?.value) || 0,
                return_money_source: tr.querySelector('[name="return_money_source[]"]')?.value || '',
                return_purpose: tr.querySelector('[name="return_purpose[]"]')?.value || '',
                note: tr.querySelector('[name="money_note[]"]')?.value || ''
            };
            allData.return_money.push(rowData);
        }
        // ��������
        else if (type === 'deposit_money') {
            const rowData = {
                store_amount: parseFloat(tr.querySelector('[name="deposit_amount[]"]')?.value) || 0,
                store_source: tr.querySelector('[name="deposit_source[]"]')?.value || '',
                note: tr.querySelector('[name="money_note[]"]')?.value || ''
            };
            allData.deposit_money.push(rowData);
        }
    });
    
    // console.log(allData);
    return allData;
}

// ���´�����Դѡ���
function updateStoreMaterialSelects() {
    // console.log('��ʼ�������д�����Դѡ���...'); // ����������ע�͵�
    
    // ȷ�����Ϲ�Ӧ�������Ѽ���
    if (!window.goldSuppliers || window.goldSuppliers.length === 0) {
        console.log('���Ϲ�Ӧ������δ���أ�ʹ��Ĭ������');
        useDefaultSuppliers();
        return; // �ȴ���һ�ε���
    }
    
    const storeSourceSelects = document.querySelectorAll('.store-material-source');
    console.log(`�ҵ� ${storeSourceSelects.length} ��������Դѡ���`);
    
    let updatedCount = 0;
    
    storeSourceSelects.forEach((select, index) => {
        // ���浱ǰֵ
        const currentValue = select.value;
        console.log(`���´�����Դѡ��� #${index + 1}����ǰֵ: "${currentValue}"`);
        
        try {
            // ��ʼ��ѡ���
            initializeStoreMaterialSource(select);
            
            // �����֮ǰ��ֵ�����Իָ�
            if (currentValue) {
                // ���ѡ���Ƿ����
                let optionExists = false;
                for (let i = 0; i < select.options.length; i++) {
                    if (select.options[i].value === currentValue) {
                        select.selectedIndex = i;
                        optionExists = true;
                        updatedCount++;
                        console.log(`�ҵ���ѡ�д�����Դ: ${currentValue}`);
                        break;
                    }
                }
                
                // ���ѡ����ڣ������
                if (!optionExists && currentValue !== '') {
                    const option = document.createElement('option');
                    option.value = currentValue;
                    option.textContent = currentValue;
                    select.appendChild(option);
                    select.value = currentValue;
                    updatedCount++;
                    console.log(`��Ӳ�ѡ�д�����Դ: ${currentValue}`);
                }
            }
        } catch (error) {
            console.error(`���´�����Դѡ��� #${index + 1} ����:`, error);
        }
    });
    
    console.log(`�Ѹ��� ${storeSourceSelects.length} ��������Դѡ��򣬻ָ��� ${updatedCount} ��֮ǰ��ֵ`);
    
    // ����Ҳ����κδ�����Դѡ��򣬿�����������δ����
    if (storeSourceSelects.length === 0) {
        console.log('δ�ҵ��κδ�����Դѡ��򣬿�����������δ����');
    }
}

// ɾ���ظ���updateAllSelects������ʹ�������Ż��汾�ĺ���

// ר�������޸�������Դѡ���ĺ���
function fixStoreMaterialSources() {
    console.log('��ʼ�޸�������Դѡ���...');
    
    // ��ȡ���д�����Դѡ���
    const storeSourceSelects = document.querySelectorAll('.store-material-source');
    if (storeSourceSelects.length === 0) {
        console.log('δ�ҵ��κδ�����Դѡ��򣬿�������δ�����κδ��ϱ����');
        return;
    }
    
    console.log(`�ҵ� ${storeSourceSelects.length} ��������Դѡ�����Ҫ�޸�`);
    
    // ȷ���й�Ӧ�����ݿ���
    if (!window.goldSuppliers || window.goldSuppliers.length === 0) {
        console.warn('���Ϲ�Ӧ������δ���أ�����API�ӿ�');
        return; // û�����ݾͲ�����
    }
    
    // ��ÿ��ѡ�������޸�
    storeSourceSelects.forEach((select, index) => {
        // ���浱ǰֵ
        const currentValue = select.value;
        console.log(`�޸�������Դѡ��� #${index + 1}����ǰֵ: "${currentValue}"`);
        
        try {
            // �������ѡ������һ����ѡ��
            while (select.options.length > 1) {
                select.remove(1);
            }
            
            // ������й�Ӧ��ѡ��
            window.goldSuppliers.forEach(supplier => {
                const option = document.createElement('option');
                option.value = supplier.name;
                option.textContent = supplier.name;
                select.appendChild(option);
            });
            
            // ��Ӷ��ⳣ��ѡ��
            ['�ϲ����', '�ֽ�', '����ת��', '֧����', '΢��'].forEach(value => {
                // ���ѡ���Ƿ��Ѵ���
                let exists = false;
                for (let i = 0; i < select.options.length; i++) {
                    if (select.options[i].value === value) {
                        exists = true;
                        break;
                    }
                }
                
                if (!exists) {
                    const option = document.createElement('option');
                    option.value = value;
                    option.textContent = value;
                    select.appendChild(option);
                }
            });
            
            // �����֮ǰ��ֵ�����Իָ������
            if (currentValue && currentValue !== '') {
                // ���ѡ���Ƿ����
                let exists = false;
                for (let i = 0; i < select.options.length; i++) {
                    if (select.options[i].value === currentValue) {
                        select.selectedIndex = i;
                        exists = true;
                        console.log(`�ѻָ�ѡ��ֵ: ${currentValue}`);
                        break;
                    }
                }
                
                // ��������ڣ������
                if (!exists) {
                    const option = document.createElement('option');
                    option.value = currentValue;
                    option.textContent = currentValue;
                    select.appendChild(option);
                    select.value = currentValue;
                    console.log(`����Ӳ�ѡ��δֵ֪: ${currentValue}`);
                }
            }
            
            console.log(`ѡ��� #${index + 1} ������ ${select.options.length} ��ѡ��`);
        } catch (error) {
            console.error(`�޸�������Դѡ��� #${index + 1} ����:`, error);
        }
    });
    
    console.log(`����� ${storeSourceSelects.length} ��������Դѡ�����޸�`);
}

// ���ҳ�������ɺ�Ĵ���
window.addEventListener('load', function() {
    // console.log('ҳ����ȫ������ɣ���ʼ�����޸�...'); // ����������ע�͵�

    // ����ִ�����յ�ѡ����޸�
    // console.log('��ʼ���յ�ѡ����޸�'); // ����������ע�͵�
    fixStoreMaterialSources();
    // updateAllSelects(); // ��ͨ�����ظ����������ݼ�����ɺ�ִ�й������ﲻ��Ҫ�ظ�����

    // ��鲢ȷ����ʾ���ϱ��
    const isEditMode = window.location.href.includes('/edit');
    if (isEditMode) {
        ensureDepositMaterialTableExists();
    }
});

// ���ȷ�����ϱ����ڵĺ���
function ensureDepositMaterialTableExists() {
    console.log('���ڼ����ϱ���Ƿ����...');
    const depositMaterialTable = document.querySelector('.transaction-row[data-type="deposit_material"]');
    
    if (!depositMaterialTable) {
        console.log('δ�ҵ����ϱ�񣬳����Զ�����...');
        // �����Զ��������
        window.isAutoCreating = true;
        addRowByType('deposit_material');
        // �����Զ��������
        window.isAutoCreating = false;
        
        // ��ʼ���´����ı��
        const newTable = document.querySelector('.transaction-row[data-type="deposit_material"]');
        if (newTable) {
            const tbody = newTable.querySelector('tbody');
            if (tbody && tbody.children.length > 0) {
                Array.from(tbody.children).forEach(row => {
                    initializeRowElements(row);
                });
            }
            console.log('���ϱ�񴴽���ɲ���ʼ��');
        } else {
            console.log('�Զ��������ϱ����ֹ - ����������');
        }
    } else {
        console.log('���ϱ���Ѵ��ڣ���������Ƿ���ȷ����');
        
        // �����ϱ���е������Ƿ���ȷ����
        const tbody = depositMaterialTable.querySelector('tbody');
        if (tbody && tbody.children.length > 0) {
            Array.from(tbody.children).forEach((row, index) => {
                const depositWeight = row.querySelector('[name="deposit_weight[]"]');
                const actualDepositWeight = row.querySelector('[name="actual_deposit_weight[]"]');
                
                // �����һ�ֶ�Ϊ�ջ�Ϊ0���������Ĭ��ֵ
                if (!depositWeight.value || depositWeight.value === '0') {
                    console.log(`���ϱ���� #${index+1} �ļ��Ͽ���Ϊ�ջ�0������Ĭ��ֵ`);
                    depositWeight.value = '0';
                }
                
                if (!actualDepositWeight.value || actualDepositWeight.value === '0') {
                    console.log(`���ϱ���� #${index+1} ��ʵ�ʵ���Ϊ�ջ�0������Ĭ��ֵ`);
                    actualDepositWeight.value = '0';
                }
                
                // ���´�����ļ���
                updateDepositLoss(depositWeight);
            });
        }
    }
    
    // �����������м��ϱ��ĸ���
    const allDepositWeights = document.querySelectorAll('[name="deposit_weight[]"]');
    console.log(`�ҵ� ${allDepositWeights.length} �����Ͽ����ֶΣ���������`);
    allDepositWeights.forEach(el => updateDepositLoss(el));
}

// ���ȫ�ֱ�ǣ���ֹ�Զ��������ϱ��
window.noAutoCreateDepositMaterial = true;

// ��дaddRowByType���������ؼ��ϱ��Ĵ�������
const originalAddRowByType = window.addRowByType;
if (originalAddRowByType) {
    window.addRowByType = function(type) {
        // ֻ���Զ�����ʱ���ؼ��ϱ�񴴽����������ֶ������ť����
        if (type === 'deposit_material' && window.noAutoCreateDepositMaterial && window.isAutoCreating) {
            console.log('���ص��Զ����ϱ�񴴽�����');
            
            // ����Ƿ���ʵ�ʵļ�������
            const hasRealDepositData = window.hasValidDepositData || false;
            
            if (!hasRealDepositData) {
                console.log('��ֹ�Զ����ϱ�񴴽� - û����Ч����');
                window.isAutoCreating = false;
                return false; // ��ֹ����
            }
        }
        
        // ����ԭʼ����
        return originalAddRowByType.apply(this, arguments);
    };
}

// ���Զ��������ϱ��
const isEditMode = window.location.href.includes('/edit');
if (isEditMode) {
    console.log('�༭ģʽ - ���ϱ��ֻ����ʵ������ʱ��ʾ');
}

// �Ƴ����ؼ��ϱ�񴴽��Ĵ���
console.log('�������ֶ���Ӽ��ϱ���û���������ʹ�ü��ϱ��ť');

// ��������޸ļ�������
function setupDataChangeListeners() {
    // �����������仯
    const formInputs = document.querySelectorAll('input, select, textarea');
    formInputs.forEach(input => {
        input.addEventListener('change', () => {
            console.log('��⵽������ݱ仯');
            window.dataModified = true;
        });
        
        if (input.tagName === 'INPUT' && input.type === 'text' || input.type === 'number') {
            input.addEventListener('input', () => {
                console.log('��⵽�����������');
                window.dataModified = true;
            });
        }
    });

    // ������̬��ӵ���
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.addedNodes.length > 0 || mutation.removedNodes.length > 0) {
                console.log('��⵽DOM�仯');
                window.dataModified = true;
                
                // Ϊ����ӵ�Ԫ�����ü�����
                const newInputs = document.querySelectorAll('input, select, textarea');
                newInputs.forEach(input => {
                    if (!input.hasChangeListener) {
                        input.addEventListener('change', () => {
                            console.log('��⵽�����Ԫ�����ݱ仯');
                            window.dataModified = true;
                        });
                        
                        if (input.tagName === 'INPUT' && (input.type === 'text' || input.type === 'number')) {
                            input.addEventListener('input', () => {
                                console.log('��⵽�����Ԫ����������');
                                window.dataModified = true;
                            });
                        }
                        
                        input.hasChangeListener = true;
                    }
                });
            }
        });
    });
    
    observer.observe(document.getElementById('transactions-container'), {
        childList: true,
        subtree: true
    });
    
    console.log('�����޸ļ�����������');
}

// ��Ҫ��ҳ���ʼ�������� - �Ѻϲ��ظ���DOMContentLoaded�¼�
// �����ظ���DOMContentLoaded�¼��������ѱ��Ƴ��Ա����ظ�ִ��

// ���水ť�¼������߼��Ѻϲ�����Ҫ��DOMContentLoaded��������

// �޸� submitForm �������������ݻع��߼�
function submitForm() {
    const isEditMode = document.getElementById('transaction-id').value !== '';
    
    // ����Ǳ༭ģʽ����Ҫ���ԭʼ���ݻع���Ϣ
    if (isEditMode && window.originalFormData) {
        // ��������ǽ�ԭʼ����һ�����͸���ˣ���˻��Ȼع�ԭʼ���ݣ���Ӧ��������
        // ��������ȷ�����ݼ������ȷ��
        
        // ��ȡ����¼���б������
        const formData = logTableData();
        
        // ��ȡ������Ҫ��Ϣ
        const supplierId = document.getElementById('supplier_id').value;
        const businessDate = document.getElementById('business_date').value;
        const note = document.getElementById('notes').value;
        
        // ��ȡ��ʷ�������
        const previousOwedGoldElement = document.getElementById('previous-owed-gold');
        const previousDepositGoldElement = document.getElementById('previous-deposit-gold');
        const previousOwedAmountElement = document.getElementById('previous-owed-amount');
        const previousDepositAmountElement = document.getElementById('previous-deposit-amount');

        // === �����ռ��ͷ����������ݣ�ͳһʹ��API��̬���� ===
        console.log('�����ռ��������ݣ�ͳһʹ��API��̬����');

        // �����ύ���ݶ���
        const data = {
            supplier_id: supplierId,
            business_date: businessDate,
            notes: note,
            material_transactions: [],
            money_transactions: [],
            is_edit_mode: true,
            original_data: window.originalFormData, // ���ԭʼ����
            // === ���ٷ����������ݣ�ͳһʹ��API��̬���� ===

            // ��ӱ������
            material_transactions: [
                ...formData.return_material.map(item => ({
                    return_weight: item.return_weight,
                    return_material_type: item.material_type,
                    return_source: item.material_source,
                    wastage_loss: item.wastage_loss,
                    note: item.note
                })),
                ...formData.store_material.map(item => ({
                    store_weight: item.store_weight,
                    store_material_type: item.material_type,
                    store_source: item.material_source,
                    note: item.note
                })),
                ...formData.deposit_material.map(item => ({
                    deposit_weight: item.deposit_weight,
                    deposit_material_type: item.material_type,
                    actual_deposit_weight: item.actual_deposit_weight,
                    deposit_loss: item.deposit_loss,
                    note: item.note
                }))
            ],
            money_transactions: [
                ...formData.return_money.map(item => ({
                    return_amount: item.return_amount,
                    return_source: item.return_money_source,
                    return_purpose: item.return_purpose || '����',
                    note: item.note
                })),
                ...formData.deposit_money.map(item => ({
                    store_amount: item.store_amount,
                    store_source: item.store_source,
                    note: item.note
                })),
                ...formData.buy_material.map(item => ({
                    return_amount: item.buy_total,
                    return_source: '',
                    return_purpose: '����',
                    return_material_type: item.material_type,
                    return_weight: item.buy_weight,
                    note: item.note
                }))
            ]
        };
        
        // �������ݵ�������
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const url = `/supplier_transactions/${document.getElementById('transaction-id').value}/edit`;
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('������Ӧ������');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: '����ɹ�',
                    text: '��Ӧ��������¼�Ѹ���'
                }).then(() => {
                    window.location.href = '/supplier_transactions';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '����ʧ��',
                    text: data.message || '����δ֪����'
                });
            }
        })
        .catch(error => {
            console.error('�ύ����:', error);
            Swal.fire({
                icon: 'error',
                title: '�ύ����',
                text: error.message
            });
        });
    } else {
        // ����ģʽ��ʹ��ԭ���߼�
        // ��ȡ����¼���б������
        const formData = logTableData();
        
        // ��ȡ������Ҫ��Ϣ
        const supplierId = document.getElementById('supplier_id').value;
        const businessDate = document.getElementById('business_date').value;
        const note = document.getElementById('notes').value;
        
        // �����ύ���ݶ���
        const data = {
            supplier_id: supplierId,
            business_date: businessDate,
            notes: note,
            material_transactions: [],
            money_transactions: [],
            
            // ��ӱ������
            material_transactions: [
                ...formData.return_material.map(item => ({
                    return_weight: item.return_weight,
                    return_material_type: item.material_type,
                    return_source: item.material_source,
                    wastage_loss: item.wastage_loss,
                    note: item.note
                })),
                ...formData.store_material.map(item => ({
                    store_weight: item.store_weight,
                    store_material_type: item.material_type,
                    store_source: item.material_source,
                    note: item.note
                })),
                ...formData.deposit_material.map(item => ({
                    deposit_weight: item.deposit_weight,
                    deposit_material_type: item.material_type,
                    actual_deposit_weight: item.actual_deposit_weight,
                    deposit_loss: item.deposit_loss,
                    note: item.note
                }))
            ],
            money_transactions: [
                ...formData.return_money.map(item => ({
                    return_amount: item.return_amount,
                    return_source: item.return_money_source,
                    return_purpose: item.return_purpose || '����',
                    note: item.note
                })),
                ...formData.deposit_money.map(item => ({
                    store_amount: item.store_amount,
                    store_source: item.store_source,
                    note: item.note
                })),
                ...formData.buy_material.map(item => ({
                    return_amount: item.buy_total,
                    return_source: '',
                    return_purpose: '����',
                    return_material_type: item.material_type,
                    return_weight: item.buy_weight,
                    note: item.note
                }))
            ]
        };
        
        // �������ݵ�������
        const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        const url = '/supplier_transactions/add';
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('������Ӧ������');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                Swal.fire({
                    icon: 'success',
                    title: '����ɹ�',
                    text: '��Ӧ��������¼�ѱ���'
                }).then(() => {
                    window.location.href = '/supplier_transactions';
                });
            } else {
                Swal.fire({
                    icon: 'error',
                    title: '����ʧ��',
                    text: data.message || '����δ֪����'
                });
            }
        })
        .catch(error => {
            console.error('�ύ����:', error);
            Swal.fire({
                icon: 'error',
                title: '�ύ����',
                text: error.message
            });
        });
    }
    
    return false; // ��ֹ���Ĭ���ύ
}

// ʹ�ð�ȫ��ƴ��ת����
function getChineseInitials(text) {
    // ���ƴ��ת�����Ƿ����
    if (window.PinyinConverter && typeof window.PinyinConverter.getInitials === 'function') {
        return window.PinyinConverter.getInitials(text);
    }

    // ���ת���������ã�ʹ�ü򵥵ı��÷���
    console.warn('PinyinConverter�����ã�ʹ�ñ��÷���');
    let initials = '';
    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        if (/[a-zA-Z0-9]/.test(char)) {
            initials += char.toLowerCase();
        } else {
            // �򵥵������ַ�����
            const code = char.charCodeAt(0);
            if (code >= 0x4E00 && code <= 0x9FFF) {
                const letterIndex = Math.floor((code - 0x4E00) / 800) % 26;
                initials += String.fromCharCode(97 + letterIndex);
            }
        }
    }
    return initials;
}

// ʹ�ð�ȫ��ƴ��ת������������ƥ��
function smartMatch(text, query) {
    // ���ƴ��ת�����Ƿ����
    if (window.PinyinConverter && typeof window.PinyinConverter.smartMatch === 'function') {
        return window.PinyinConverter.smartMatch(text, query);
    }

    // ���ת���������ã�ʹ�ü򵥵ı���ƥ��
    console.warn('PinyinConverter�����ã�ʹ�ñ�����������');
    const lowerText = text.toLowerCase();
    const lowerQuery = query.toLowerCase();

    // �򵥵�ֱ��ƥ��
    return lowerText.includes(lowerQuery);
}



// ��ʼ����Ӧ����������
function initializeSupplierSearch() {
    // ʹ��ȫ�ֹ�Ӧ������
    const suppliersData = window.suppliersData || [];
    console.log('��ʼ����Ӧ���������ܣ���Ӧ������:', suppliersData ? suppliersData.length : 0);

    // ���û�й�Ӧ�����ݣ���ǰ����
    if (!suppliersData || !Array.isArray(suppliersData) || suppliersData.length === 0) {
        console.warn('û�й�Ӧ�����ݣ������������ܳ�ʼ��');
        return;
    }

    const searchInput = document.getElementById('supplier-search');
    const hiddenInput = document.getElementById('supplier-id');
    const resultsContainer = document.querySelector('.supplier-search-results');

    console.log('����Ԫ�ؼ��:', {
        searchInput: !!searchInput,
        hiddenInput: !!hiddenInput,
        resultsContainer: !!resultsContainer
    });

    // ���������¼�
    searchInput.addEventListener('input', function() {
        const query = this.value.toLowerCase();
        console.log('��Ӧ����������:', query);

        if (query.length === 0) {
            resultsContainer.style.display = 'none';
            document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
                el.remove();
            });
            hiddenInput.value = '';
            updateSupplierInfo();
            return;
        }

        // �Ż��Ĺ�Ӧ�������߼� - ʹ�����ݿ�ƴ������
        const filteredSuppliers = suppliersData.filter(function(supplier) {
            const name = supplier.name.toLowerCase();
            const pinyin = (supplier.pinyin || '').toLowerCase();
            const initials = (supplier.pinyin_initials || '').toLowerCase();

            // 1. ����ֱ�Ӱ���ƥ�䣨������ȼ���
            if (name.includes(query)) {
                return true;
            }

            // 2. ����ĸ��ȷ��ͷƥ�� - ���� z ��ʾ z ��ͷ�Ľ����zh ��ʾ zh ��ͷ�Ľ��
            if (initials.startsWith(query)) {
                return true;
            }

            // 3. ����ƴ���ͷƥ��
            if (pinyin.startsWith(query)) {
                return true;
            }

            // 4. ƴ�����ƥ�� - ֧��ƴ����������ڿ�ͷƥ��
            if (pinyin.length > 0) {
                // �ָ�ƴ��Ϊ��ڣ���chenzhongdong -> ['chen', 'zhong', 'dong']��
                const pinyinParts = pinyin.match(/[a-z]+/g) || [];
                for (let j = 0; j < pinyinParts.length; j++) {
                    if (pinyinParts[j].startsWith(query)) {
                        return true;
                    }
                }
            }

            // 5. ����ĸ����ƥ�� - ֧�ֿ��ַ����ƥ�䣨�� cz ƥ�� chenzhongdong��
            if (query.length > 1 && initials.length > 0) {
                const queryChars = query.split('');
                const initialsChars = initials.split('');

                // ����ѯ�ַ��Ƿ�������ĸ�а�˳�����
                let queryIndex = 0;
                for (let i = 0; i < initialsChars.length && queryIndex < queryChars.length; i++) {
                    if (initialsChars[i] === queryChars[queryIndex]) {
                        queryIndex++;
                    }
                }

                // ������в�ѯ�ַ�����˳���ҵ��ˣ���ƥ��
                if (queryIndex === queryChars.length) {
                    return true;
                }
            }

            // 6. ���ַ�����ĸƥ��
            if (query.length === 1 && initials.includes(query)) {
                return true;
            }

            // 7. ��̬ƴ��ת������Ϊ����÷���
            if (window.PinyinConverter && typeof window.PinyinConverter.smartMatch === 'function') {
                return window.PinyinConverter.smartMatch(supplier.name, query);
            }

            return false;
        });

        console.log('���˺�Ĺ�Ӧ������:', filteredSuppliers.length);

        // ��ʾ�������
        if (filteredSuppliers.length > 0) {
            var html = '';
            filteredSuppliers.forEach(function(supplier) {
                html += '<div class="search-result-item" data-supplier-id="' + supplier.id + '" ' +
                       'style="padding: 6px 12px; cursor: pointer; border-bottom: 1px solid #eee; background: white; font-size: 14px; line-height: 1.4;" ' +
                       'onmouseover="this.style.backgroundColor=\'#f8f9fa\'" onmouseout="this.style.backgroundColor=\'white\'" ' +
                       'data-info=\'' + JSON.stringify({
                           name: supplier.name,
                           supplier_type: supplier.supplier_type,
                           contact_person: supplier.contact_person,
                           phone: supplier.phone,
                           owed_amount: supplier.owed_amount || 0,
                           owed_gold: supplier.owed_gold || 0,
                           deposit_amount: supplier.deposit_amount || 0,
                           deposit_gold: supplier.deposit_gold || 0
                       }) + '\'>' +
                       supplier.name +
                       '</div>';
            });
            resultsContainer.innerHTML = html;

            // �����������ӵ�body�У������������ƣ���ɹ���¼��ҳ��ȫһ�£�
            var inputElement = searchInput;
            var rect = inputElement.getBoundingClientRect();

            // �Ƴ����е������������
            document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
                el.remove();
            });

            // �����µ����������������ӵ�body
            var globalResults = document.createElement('div');
            globalResults.className = 'supplier-search-results-global';
            globalResults.innerHTML = html;
            document.body.appendChild(globalResults);

            console.log('������λ��Ϣ:', {
                rect: rect,
                resultTop: rect.bottom + 1,
                resultLeft: rect.left,
                resultWidth: rect.width
            });

            // ������ʽ����ɹ���¼��ҳ��ȫһ�£�
            globalResults.style.position = 'fixed';
            globalResults.style.top = (rect.bottom + 1) + 'px';
            globalResults.style.left = rect.left + 'px';
            globalResults.style.width = rect.width + 'px';
            globalResults.style.maxHeight = '200px';
            globalResults.style.overflowY = 'auto';
            globalResults.style.zIndex = '999999';
            globalResults.style.background = 'white';
            globalResults.style.border = '1px solid #ced4da';
            globalResults.style.borderRadius = '4px';
            globalResults.style.boxShadow = '0 4px 12px rgba(0,0,0,0.25)';
            globalResults.style.marginTop = '0px';
            globalResults.style.display = 'block';

            // ����ԭ�е������������
            resultsContainer.style.display = 'none';
        } else {
            resultsContainer.style.display = 'none';
            // �Ƴ�ȫ���������
            document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
                el.remove();
            });
        }
    });

    // ��������������ɹ���¼��ҳһ�£�
    document.addEventListener('click', function(e) {
        if (e.target.closest('.search-result-item')) {
            const item = e.target.closest('.search-result-item');
            const supplierId = item.getAttribute('data-supplier-id');
            const supplierInfo = JSON.parse(item.getAttribute('data-info'));

            console.log('ѡ��Ӧ��:', supplierInfo.name, 'ID:', supplierId);

            searchInput.value = supplierInfo.name;
            hiddenInput.value = supplierId;

            // ���������������
            resultsContainer.style.display = 'none';
            document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
                el.remove();
            });

            // ������Ӧ����Ϣ����
            updateSupplierInfo();
        }
    });

    // ��������ط����������������ɹ���¼��ҳһ�£�
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.supplier-search-container') && !e.target.closest('.supplier-search-results-global')) {
            resultsContainer.style.display = 'none';
            document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
                el.remove();
            });
        }
    });

    // ���ڹ����������Сʱ���������������ɹ���¼��ҳһ�£�
    window.addEventListener('scroll', function() {
        document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
            el.remove();
        });
        resultsContainer.style.display = 'none';
    });

    window.addEventListener('resize', function() {
        document.querySelectorAll('.supplier-search-results-global').forEach(function(el) {
            el.remove();
        });
        resultsContainer.style.display = 'none';
    });
}

// ɾ���ظ���updateSupplierInfo������ʹ�õ�һ���汾

// ��ȫ��ʼ��ȫ�ֱ���
function initializeGlobalVariables() {
    if (!window.goldSuppliers) {
        window.goldSuppliers = [];
    }
    if (!window.oldMaterials) {
        window.oldMaterials = [];
    }
    if (!window.accounts) {
        window.accounts = [];
    }
    console.log('ȫ�ֱ�����ʼ�����');
}

// ͳһ��ҳ�湦�ܳ�ʼ������
function initializePageFeatures() {
    // ��ʼ����������
    initializeBasicFeatures();

    // ��ʼ��ҵ�����ڼ�����
    initializeBusinessDateListener();

    // ��ʼ���߼�����
    initializeAdvancedFeatures();
}

// ��ʼ����Ӧ�������������߼�����
function initializeAdvancedFeatures() {
    // ��ȫ�س�ʼ����Ӧ����������
    try {
        initializeSupplierSearch();
    } catch (searchError) {
        console.error('��ʼ����Ӧ����������ʧ��:', searchError);
    }

    // ����Ǳ༭ģʽ����ѡ��Ӧ�̣�������Ϣ����
    const supplierIdInput = document.getElementById('supplier-id');
    if (supplierIdInput && supplierIdInput.value) {
        try {
            updateSupplierInfo();
        } catch (updateError) {
            console.error('���¹�Ӧ����Ϣʧ��:', updateError);
        }
    }
}

// ɾ���ظ���removeTableRow����

// ǿ�����ñ���п�ͼ��ĺ���
function forceTableColumnWidths() {
    console.log('��ʼǿ�����ñ���п�ͼ��...');

    // ���ȴ������������ļ��
    const transactionRows = document.querySelectorAll('#transactions-container .transaction-row');
    // ɾ��ǿ����ʽ���ã���CSS��Ȼ��Ч

    // �������б��
    const tables = document.querySelectorAll('#transactions-container table.table');

    tables.forEach((table, index) => {
        console.log(`������ ${index + 1}:`, table);

        // ɾ��ǿ����ʽ���ã���CSS��Ȼ��Ч

        console.log(`��� ${index + 1} �п��������`);
    });

    // ǿ������������ʽ
    // ɾ������ǿ����ʽ����

    console.log('���б���п�ͼ���������');
}

// ҳ�������ɺ�ִ��
document.addEventListener('DOMContentLoaded', function() {
    // �ӳ�ִ����ȷ��������ʽ���Ѽ���
    setTimeout(forceTableColumnWidths, 100);

    // ����������ݱ仯
    const observer = new MutationObserver(function(mutations) {
        let shouldUpdate = false;
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                for (let node of mutation.addedNodes) {
                    if (node.nodeType === 1 && (node.tagName === 'TABLE' || node.querySelector('table'))) {
                        shouldUpdate = true;
                        break;
                    }
                }
            }
        });

        if (shouldUpdate) {
            setTimeout(forceTableColumnWidths, 50);
        }
    });

    const container = document.getElementById('transactions-container');
    if (container) {
        observer.observe(container, {
            childList: true,
            subtree: true
        });
    }
});
</script>

<!-- �����Զ����alert���Ʒ -->
<script src="{{ url_for('static', filename='js/custom_alert.js') }}"></script>
<!-- ����pinyin-pro������׼ȷ������תƴ������ĸ -->
<script src="https://unpkg.com/pinyin-pro@3.19.6/dist/index.js"></script>
<!-- ���밲ȫ��ƴ��ת��ģ�� -->
<script src="{{ url_for('static', filename='js/pinyin_converter.js') }}"></script>
{% endblock %}
